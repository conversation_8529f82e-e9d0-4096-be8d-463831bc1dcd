import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import TypewriterText from '../../contact/components/TypewriterText';

const Services: React.FC = () => {
  const { t } = useTranslation();
  const [activeService, setActiveService] = useState<string | null>(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  const services = [
    {
      key: 'propertyManagement',
      icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
      color: 'bg-blue-500'
    },
    {
      key: 'consulting',
      icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      color: 'bg-purple-500'
    },
    {
      key: 'rental',
      icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
      color: 'bg-italian-green'
    }
  ];

  const testimonials = [
    {
      id: 1,
      nameKey: 'services.testimonials.testimonial1.name',
      roleKey: 'services.testimonials.testimonial1.role',
      contentKey: 'services.testimonials.testimonial1.content',
      image: 'https://randomuser.me/api/portraits/women/32.jpg'
    },
    {
      id: 2,
      nameKey: 'services.testimonials.testimonial2.name',
      roleKey: 'services.testimonials.testimonial2.role',
      contentKey: 'services.testimonials.testimonial2.content',
      image: 'https://randomuser.me/api/portraits/men/46.jpg'
    },
    {
      id: 3,
      nameKey: 'services.testimonials.testimonial3.name',
      roleKey: 'services.testimonials.testimonial3.role',
      contentKey: 'services.testimonials.testimonial3.content',
      image: 'https://randomuser.me/api/portraits/women/56.jpg'
    }
  ];

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen py-16 relative overflow-hidden">
      {/* Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-italian-green/10 to-transparent"></div>
      <div className="absolute top-40 left-20 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/20 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <motion.div
          className="text-center mb-16 relative"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Badge */}
          <motion.div 
            className="inline-block mb-4"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <span className="bg-italian-green/10 text-italian-green px-4 py-2 rounded-full text-sm font-semibold uppercase tracking-wider">
              {t('navigation.services')}
            </span>
          </motion.div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            <TypewriterText text={t('services.title')} delay={70} />
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
            {t('services.subtitle')}
          </p>
          
          {/* Animated Underline */}
          <motion.div
            className="h-1 w-24 bg-italian-green mx-auto rounded-full"
            initial={{ width: 0 }}
            animate={{ width: 96 }}
            transition={{ duration: 1, delay: 0.6 }}
          />
        </motion.div>

        {/* Services Grid */}
        <motion.div
          className="grid md:grid-cols-3 gap-8 mb-20"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {services.map((service) => (
            <motion.div
              key={service.key}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden group relative"
              variants={itemVariants}
              whileHover={{ y: -10, transition: { duration: 0.3 } }}
              onMouseEnter={() => setActiveService(service.key)}
              onMouseLeave={() => setActiveService(null)}
            >
              {/* Top Decoration */}
              <div className={`h-2 w-full ${service.color}`}></div>
              
              {/* Icon */}
              <div className="absolute -right-6 -top-6 w-24 h-24 rounded-full bg-gray-100 dark:bg-gray-700 opacity-20"></div>
              
              {/* Content */}
              <div className="p-8">
                <div className="mb-6 relative">
                  <div className={`w-16 h-16 rounded-full ${service.color} bg-opacity-20 dark:bg-opacity-30 flex items-center justify-center mb-4 transform transition-transform group-hover:scale-110 duration-300`}>
                    <svg
                      className={`w-8 h-8 ${service.color === 'bg-italian-green' ? 'text-italian-green' : service.color === 'bg-blue-500' ? 'text-blue-500' : 'text-purple-500'}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d={service.icon} />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-italian-green transition-colors duration-300">
                    {t(`services.${service.key}.title`)}
                  </h3>
                </div>
                
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {t(`services.${service.key}.description`)}
                </p>
                
                <div className="pt-4 border-t border-gray-100 dark:border-gray-700 flex justify-between items-center">
                  <ul className="space-y-2">
                    {['feature1', 'feature2', 'feature3'].map((feature) => (
                      <li 
                        key={feature} 
                        className="flex items-center text-gray-600 dark:text-gray-400 text-sm"
                      >
                        <svg className="w-4 h-4 mr-2 text-italian-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        {t(`services.${service.key}.${feature}`)}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <motion.div
                  className="mt-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={activeService === service.key ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Link
                    to={`/contact?service=${service.key}`}
                    className="inline-flex items-center text-italian-green hover:text-italian-green-dark transition-colors"
                  >
                    {t('services.learnMore')}
                    <svg className="w-4 h-4 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>
        
        {/* Process Section */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {t('services.process.title')}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {t('services.process.subtitle')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-8">
            {[1, 2, 3, 4].map((step) => (
              <motion.div
                key={step}
                className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg relative"
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
              >
                {/* Step Number */}
                <div className="absolute -top-5 left-6">
                  <div className="w-10 h-10 rounded-full bg-italian-green text-white flex items-center justify-center font-bold text-lg">
                    {step}
                  </div>
                </div>
                
                <div className="pt-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                    {t(`services.process.step${step}.title`)}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {t(`services.process.step${step}.description`)}
                  </p>
                </div>
                
                {/* Connection Line */}
                {step < 4 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 w-8 h-1 bg-gray-200 dark:bg-gray-700 z-0">
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full bg-italian-green"></div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        {/* Testimonials */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {t('services.testimonials.title')}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {t('services.testimonials.subtitle')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial) => (
              <motion.div
                key={testimonial.id}
                className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
                whileHover={{ y: -5, boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)' }}
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                    <img 
                      src={testimonial.image} 
                      alt={t(testimonial.nameKey)}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 dark:text-white">{t(testimonial.nameKey)}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t(testimonial.roleKey)}</p>
                  </div>
                </div>
                
                <p className="text-gray-600 dark:text-gray-300 italic">
                  "{t(testimonial.contentKey)}"
                </p>
                
                <div className="mt-4 flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <svg 
                      key={star} 
                      className="w-5 h-5 text-yellow-400" 
                      fill="currentColor" 
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.462a1 1 0 00.95-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        {/* CTA Section */}
        <motion.div
          className="bg-italian-green/10 dark:bg-italian-green/20 rounded-2xl p-12 text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t('services.cta.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            {t('services.cta.subtitle')}
          </p>
          <Link
            to="/contact"
            className="inline-flex items-center bg-italian-green text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-italian-green-dark transition-colors shadow-lg"
          >
            {t('services.cta.button')}
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </motion.div>
      </div>
    </div>
  );
};

export default Services;
