import { ThemeState } from '../core/theme/themeSlice';
import { LanguageState } from '../core/i18n/languageSlice';
import { NavigationState } from '../core/navigation/navigationSlice';
import { PropertiesState } from '../features/properties/propertiesSlice';
import { FiltersState } from '../features/properties/filtersSlice';
import { UserPreferencesState } from '../features/properties/userPreferencesSlice';

export interface RootState {
  theme: ThemeState;
  language: LanguageState;
  navigation: NavigationState;
  properties: PropertiesState;
  filters: FiltersState;
  userPreferences: UserPreferencesState;
}

// Re-export types from slices for convenience
export type {
  ThemeState,
  LanguageState,
  NavigationState,
  PropertiesState,
  FiltersState,
  UserPreferencesState,
};
