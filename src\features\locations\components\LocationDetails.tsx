import React from 'react';

interface LocationFeature {
  id: string;
  name: string;
  description?: string;
}

interface LocationData {
  name: string;
  description: string;
  features: LocationFeature[];
}

interface LocationDetailsProps {
  location: LocationData;
}

const LocationDetails: React.FC<LocationDetailsProps> = ({ location }) => {
  return (
    <div className="flex flex-col space-y-6">
      <div className="space-y-4">
        <h2 className="text-3xl font-semibold text-primary">{location.name}</h2>
        <p className="text-neutral text-lg leading-relaxed">{location.description}</p>
      </div>
      
      {location.features.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-2xl font-medium text-primary">Features</h3>
          <ul className="space-y-3">
            {location.features.map((feature) => (
              <li key={feature.id} className="flex flex-col">
                <span className="text-lg font-medium text-primary">{feature.name}</span>
                {feature.description && (
                  <span className="text-neutral mt-1">{feature.description}</span>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default LocationDetails;