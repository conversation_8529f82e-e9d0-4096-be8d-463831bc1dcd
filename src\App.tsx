import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Layout from './core/layout/Layout';
import AppRoutes from './app/routes';
import type { RootState } from './app/store';
import { AuthProvider } from './features/admin/components/AuthProvider';
import { setTranslations } from './core/i18n/languageSlice';
import { en } from './core/i18n/locales/en';
import { fr } from './core/i18n/locales/fr';
import './App.css';

const App: React.FC = () => {
  const { mode } = useSelector((state: RootState) => state.theme);
  const dispatch = useDispatch();
  const isDarkMode = mode === 'dark';

  useEffect(() => {
    // Initialize translations
    dispatch(setTranslations({
      en,
      fr
    }));
  }, [dispatch]);

  return (
    <AuthProvider>
      <Layout>
        <AnimatePresence mode="wait">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <AppRoutes />
            <ToastContainer position="bottom-right" theme={isDarkMode ? 'dark' : 'light'} />
          </motion.div>
        </AnimatePresence>
      </Layout>
    </AuthProvider>
  );
};

export default App;