import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { useContacts, Contact } from '../hooks/useContacts';

type SortField = 'created_at' | 'name' | 'subject' | 'status';
type SortOrder = 'asc' | 'desc';

export const ContactsPage: React.FC = () => {
  const { t } = useTranslation();
  const { contacts, loading, error, updateContactStatus } = useContacts();
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(order => (order === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const handleStatusChange = async (contactId: string, newStatus: string) => {
    try {
      await updateContactStatus(contactId, newStatus);
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  };

  const sortedAndFilteredContacts = React.useMemo(() => {
    let filtered = [...contacts];
    
    if (statusFilter !== 'all') {
      filtered = filtered.filter(contact => contact.status === statusFilter);
    }

    return filtered.sort((a, b) => {
      if (sortField === 'created_at') {
        return sortOrder === 'asc' 
          ? new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          : new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
      
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      }
      return aValue < bValue ? 1 : -1;
    });
  }, [contacts, statusFilter, sortField, sortOrder]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-italian-green"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/50 p-4 rounded-lg">
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  const statusColors = {
    new: 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300',
    read: 'bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-300',
    replied: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',
    archived: 'bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-300',
  };

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {t('admin.contacts')}
            </h1>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-italian-green focus:border-italian-green"
            >
              <option value="all">{t('admin.allStatuses')}</option>
              <option value="new">{t('admin.statusNew')}</option>
              <option value="read">{t('admin.statusRead')}</option>
              <option value="replied">{t('admin.statusReplied')}</option>
              <option value="archived">{t('admin.statusArchived')}</option>
            </select>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900/50">
                <tr>
                  <th
                    onClick={() => handleSort('created_at')}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    {t('admin.date')}
                    {sortField === 'created_at' && (
                      <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </th>
                  <th
                    onClick={() => handleSort('name')}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    {t('admin.name')}
                    {sortField === 'name' && (
                      <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </th>
                  <th
                    onClick={() => handleSort('subject')}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    {t('admin.subject')}
                    {sortField === 'subject' && (
                      <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('admin.contact')}
                  </th>
                  <th
                    onClick={() => handleSort('status')}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    {t('admin.status')}
                    {sortField === 'status' && (
                      <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    {t('admin.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {sortedAndFilteredContacts.map((contact) => (
                  <tr
                    key={contact.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer transition-colors"
                    onClick={() => setSelectedContact(contact)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {format(new Date(contact.created_at), 'PP')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {contact.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {contact.subject}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div>{contact.email}</div>
                      {contact.phone && <div>{contact.phone}</div>}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[contact.status as keyof typeof statusColors]}`}>
                        {t(`admin.status${contact.status.charAt(0).toUpperCase() + contact.status.slice(1)}`)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <select
                        value={contact.status}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleStatusChange(contact.id, e.target.value);
                        }}
                        onClick={(e) => e.stopPropagation()}
                        className="rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-italian-green focus:border-italian-green text-sm"
                      >
                        <option value="new">{t('admin.statusNew')}</option>
                        <option value="read">{t('admin.statusRead')}</option>
                        <option value="replied">{t('admin.statusReplied')}</option>
                        <option value="archived">{t('admin.statusArchived')}</option>
                      </select>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {selectedContact && (
        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {selectedContact.subject}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('admin.common.from')}: {selectedContact.name} ({selectedContact.email})
                {selectedContact.phone && ` • ${selectedContact.phone}`}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('admin.contacts.fields.createdAt')}: {format(new Date(selectedContact.created_at), 'PPP')}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[selectedContact.status as keyof typeof statusColors]}`}>
                {t(`admin.common.status${selectedContact.status.charAt(0).toUpperCase() + selectedContact.status.slice(1)}`)}
              </span>
              <button
                onClick={() => setSelectedContact(null)}
                className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
              >
                <span className="sr-only">{t('admin.common.close')}</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          <div className="prose dark:prose-invert max-w-none mb-4">
            <p className="whitespace-pre-wrap text-gray-600 dark:text-gray-300">
              {selectedContact.message}
            </p>
          </div>
          {selectedContact.document_url && (
            <div className="mt-4">
              <a
                href={selectedContact.document_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-italian-green hover:bg-italian-green/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-italian-green"
              >
                <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                {t('admin.common.viewAttachment')}
              </a>
            </div>
          )}
          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={() => handleStatusChange(selectedContact.id, 'read')}
              disabled={selectedContact.status !== 'new'}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedContact.status === 'new'
                  ? 'bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:hover:bg-blue-900/70'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600'
              }`}
            >
              {t('admin.common.statusRead')}
            </button>
            <button
              onClick={() => handleStatusChange(selectedContact.id, 'replied')}
              disabled={selectedContact.status !== 'read'}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedContact.status === 'read'
                  ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/50 dark:text-green-300 dark:hover:bg-green-900/70'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600'
              }`}
            >
              {t('admin.common.statusReplied')}
            </button>
            <button
              onClick={() => handleStatusChange(selectedContact.id, 'archived')}
              disabled={selectedContact.status === 'archived'}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedContact.status !== 'archived'
                  ? 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600'
              }`}
            >
              {t('admin.common.statusArchived')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};