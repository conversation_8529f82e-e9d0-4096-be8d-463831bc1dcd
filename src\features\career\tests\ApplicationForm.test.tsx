import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ApplicationForm from '../components/ApplicationForm';
import { supabase } from '@/core/supabase/client';

// Mock the translations
jest.mock('@/core/i18n/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe('ApplicationForm', () => {
  const mockProps = {
    careerId: 'test-position-1',
    careerTitle: 'Test Position',
  };

  const mockFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('renders the form with all required fields', () => {
    render(<ApplicationForm {...mockProps} />);

    expect(screen.getByLabelText(/career.application.firstName/)).toBeInTheDocument();
    expect(screen.getByLabelText(/career.application.lastName/)).toBeInTheDocument();
    expect(screen.getByLabelText(/career.application.email/)).toBeInTheDocument();
    expect(screen.getByLabelText(/career.application.phone/)).toBeInTheDocument();
    expect(screen.getByLabelText(/career.application.selfPresentation/)).toBeInTheDocument();
    expect(screen.getByLabelText(/career.application.motivation/)).toBeInTheDocument();
    expect(screen.getByLabelText(/career.application.resume/)).toBeInTheDocument();
  });

  it('submits the form successfully', async () => {
    render(<ApplicationForm {...mockProps} />);

    // Fill in the form
    fireEvent.change(screen.getByLabelText(/career.application.firstName/), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.lastName/), {
      target: { value: 'Doe' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.email/), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.phone/), {
      target: { value: '1234567890' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.selfPresentation/), {
      target: { value: 'Test presentation' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.motivation/), {
      target: { value: 'Test motivation' },
    });

    // Upload resume
    const fileInput = screen.getByLabelText(/career.application.resume/);
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Submit form
    fireEvent.click(screen.getByText(/career.application.submit/));

    // Wait for form submission
    await waitFor(() => {
      expect(supabase.storage.from).toHaveBeenCalledWith('applications');
      expect(supabase.from).toHaveBeenCalledWith('career_applications');
    });
  });

  it('shows error message when submission fails', async () => {
    // Mock the Supabase client to return an error
    (supabase.from as jest.Mock).mockImplementationOnce(() => ({
      insert: () => ({
        select: () => ({
          data: null,
          error: new Error('Test error'),
        }),
      }),
    }));

    render(<ApplicationForm {...mockProps} />);

    // Fill in the form
    fireEvent.change(screen.getByLabelText(/career.application.firstName/), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.lastName/), {
      target: { value: 'Doe' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.email/), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.phone/), {
      target: { value: '1234567890' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.selfPresentation/), {
      target: { value: 'Test presentation' },
    });
    fireEvent.change(screen.getByLabelText(/career.application.motivation/), {
      target: { value: 'Test motivation' },
    });

    // Upload resume
    const fileInput = screen.getByLabelText(/career.application.resume/);
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Submit form
    fireEvent.click(screen.getByText(/career.application.submit/));

    // Wait for error message
    await waitFor(() => {
      expect(screen.getByText(/career.application.error/)).toBeInTheDocument();
    });
  });
});
