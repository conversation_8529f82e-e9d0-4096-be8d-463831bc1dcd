import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface FiltersState {
  priceRange: {
    min: number;
    max: number;
  };
  location: string;
  propertyType: string[];
  listingType: 'all' | 'rent' | 'sale';
}

const initialState: FiltersState = {
  priceRange: {
    min: 0,
    max: 4000000,
  },
  location: '',
  propertyType: [],
  listingType: 'all',
};

const filtersSlice = createSlice({
  name: 'filters',
  initialState,
  reducers: {
    setPriceRange: (state, action: PayloadAction<{ min: number; max: number }>) => {
      state.priceRange = action.payload;
    },
    setLocation: (state, action: PayloadAction<string>) => {
      state.location = action.payload;
    },
    setPropertyType: (state, action: PayloadAction<string[]>) => {
      state.propertyType = action.payload;
    },
    setListingType: (state, action: PayloadAction<'all' | 'rent' | 'sale'>) => {
      state.listingType = action.payload;
    },
    resetFilters: (state) => {
      return initialState;
    },
  },
});

export const {
  setPriceRange,
  setLocation,
  setPropertyType,
  setListingType,
  resetFilters,
} = filtersSlice.actions;

export default filtersSlice.reducer;
