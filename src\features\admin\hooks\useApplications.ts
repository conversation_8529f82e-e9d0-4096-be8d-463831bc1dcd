import { useEffect, useState, useCallback } from 'react';
import { supabase } from '../../../core/supabase/client';
import { useSupabaseQuery } from '@core/hooks/useSupabaseQuery';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';

// Maximum number of retry attempts for failed operations
const MAX_RETRY_ATTEMPTS = 3;

// Default retry delay in milliseconds
const DEFAULT_RETRY_DELAY = 1500;

export type ApplicationStatus = 'pending' | 'accepted' | 'rejected' | 'review' | 'archived';

export interface JobApplication {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  career_id: string;
  career_title: string;
  resume_url: string;
  self_presentation?: string;
  motivation?: string;
  created_at: string;
  user_id: string;
  status: ApplicationStatus;
}

interface ApplicationsState {
  applications: JobApplication[];
  loading: boolean;
  error: string | null;
  errorDetails?: any;
  lastFetched: number | null;
  supabaseConnected: boolean;
}

const VALID_STATUS_TRANSITIONS: Record<ApplicationStatus, ApplicationStatus[]> = {
  pending: ['review', 'accepted', 'rejected', 'archived'],
  review: ['accepted', 'rejected', 'archived'],
  accepted: ['archived'],
  rejected: ['archived'],
  archived: ['pending']
};

export const useApplications = () => {
  const { t } = useTranslation();
  const query = useSupabaseQuery<JobApplication>({
    table: 'career_applications',
    orderBy: { column: 'created_at', ascending: false }
  });

  const [state, setState] = useState<ApplicationsState>({
    applications: [],
    loading: true,
    error: null,
    lastFetched: null,
    supabaseConnected: false
  });

  useEffect(() => {
    // Check Supabase connection before attempting to fetch data
    checkSupabaseConnection();
    fetchApplications();
  }, []);

  const checkSupabaseConnection = async () => {
    try {
      console.log('Verifying Supabase connection...');
      const { data, error } = await supabase.from('career_applications').select('count');
      
      if (error) {
        console.error('Supabase connection check failed:', error);
        setState(prev => ({ ...prev, supabaseConnected: false }));
        return false;
      }
      
      console.log('Supabase connection verified successfully');
      setState(prev => ({ ...prev, supabaseConnected: true }));
      return true;
    } catch (err) {
      console.error('Failed to verify Supabase connection:', err);
      setState(prev => ({ ...prev, supabaseConnected: false }));
      return false;
    }
  };

  const fetchApplications = useCallback(async (retryCount = 0) => {
    console.log(`Fetching applications data from Supabase... (Attempt ${retryCount + 1}/${MAX_RETRY_ATTEMPTS + 1})`);
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // Verify database connection is working
      const isConnected = await checkSupabaseConnection();
      if (!isConnected && retryCount < MAX_RETRY_ATTEMPTS) {
        console.warn(`Supabase connection failed, retrying in ${DEFAULT_RETRY_DELAY}ms...`);
        setTimeout(() => fetchApplications(retryCount + 1), DEFAULT_RETRY_DELAY);
        return;
      }
      
      console.log('Executing applications query...');
      const startTime = performance.now();
      
      const { data, error, status } = await supabase
        .from('career_applications')
        .select('*')
        .order('created_at', { ascending: false });
      
      const queryTime = performance.now() - startTime;
      console.log(`Query completed in ${queryTime.toFixed(2)}ms with status ${status}`);
      
      if (error) {
        console.error(`Supabase error (${status}):`, error);
        throw error;
      }
      
      console.log(`Retrieved ${data?.length || 0} application records`);
      console.log('Sample data:', data && data.length > 0 ? data[0] : 'No data available');
      
      if (!data || !Array.isArray(data)) {
        console.warn('Data is not in expected format:', data);
        throw new Error('Invalid data format received from the server');
      }
      
      // Enhanced data validation and sanitization
      const processedData = data.map(app => {
        // Validate required fields
        if (!app.id) {
          console.warn('Application missing ID:', app);
        }
        
        if (!app.email) {
          console.warn('Application missing email:', app);
        }
        
        // Sanitize and provide defaults for all fields
        return {
          ...app,
          id: app.id || `temp-${Date.now()}`,
          first_name: app.first_name ? app.first_name.trim() : '',
          last_name: app.last_name ? app.last_name.trim() : '',
          email: app.email ? app.email.trim().toLowerCase() : '',
          phone: app.phone ? app.phone.trim() : '',
          career_id: app.career_id || '',
          career_title: app.career_title ? app.career_title.trim() : 'Unknown Position',
          resume_url: app.resume_url || '',
          self_presentation: app.self_presentation || '',
          motivation: app.motivation || '',
          created_at: app.created_at || new Date().toISOString(),
          user_id: app.user_id || '',
          status: app.status || 'pending'
        };
      });
      
      console.log('Data processing complete, updating state...');
      setState(prev => ({
        ...prev,
        applications: processedData,
        loading: false,
        error: null,
        errorDetails: null,
        lastFetched: Date.now(),
        supabaseConnected: true
      }));
    } catch (err) {
      console.error('Error fetching applications:', err);
      
      // Enhanced error handling with detailed logging
      let errorMessage = 'An error occurred while fetching application data';
      let errorDetails = null;
      
      if (err instanceof Error) {
        console.error(`Error name: ${err.name}, Message: ${err.message}`);
        console.error('Error stack:', err.stack);
        errorMessage = err.message;
        errorDetails = {
          name: err.name,
          stack: err.stack,
          timestamp: new Date().toISOString()
        };
      } else if (typeof err === 'object' && err !== null) {
        errorDetails = err;
      }
      
      // Implement retry logic for recoverable errors
      if (retryCount < MAX_RETRY_ATTEMPTS) {
        const delay = DEFAULT_RETRY_DELAY * (retryCount + 1); // Exponential back-off
        console.warn(`Retrying fetchApplications in ${delay}ms... (Attempt ${retryCount + 1}/${MAX_RETRY_ATTEMPTS})`);
        setTimeout(() => fetchApplications(retryCount + 1), delay);
      } else {
        console.error('Maximum retry attempts reached. Giving up.');
        setState(prev => ({
          ...prev,
          loading: false,
          error: errorMessage,
          errorDetails
        }));
      }
    }
  }, []);

  const updateApplicationStatus = useCallback(async (id: string, newStatus: ApplicationStatus) => {
    try {
      const application = state.applications.find(a => a.id === id);
      if (!application) {
        throw new Error('Application not found');
      }

      const validTransitions = VALID_STATUS_TRANSITIONS[application.status];
      if (!validTransitions.includes(newStatus)) {
        throw new Error(`Invalid status transition from ${application.status} to ${newStatus}`);
      }

      await query.updateRecord(id, { status: newStatus });
      toast.success(t(`admin.applications.status.updated`));
      return true;
    } catch (err) {
      console.error('Error updating application status:', err);
      toast.error(t(`admin.applications.status.error`));
      return false;
    }
  }, [state.applications, query]);

  const getConnectionInfo = () => {
    return {
      connected: state.supabaseConnected,
      lastChecked: new Date().toISOString(),
      lastFetched: state.lastFetched ? new Date(state.lastFetched).toISOString() : null,
      dataAge: state.lastFetched ? Date.now() - state.lastFetched : null
    };
  };

  return {
    applications: query.data,
    loading: query.loading,
    error: query.error,
    errorDetails: query.errorDetails,
    lastFetched: query.lastFetched,
    connectionInfo: getConnectionInfo(),
    refetch: query.refetch,
    updateApplicationStatus,
    checkConnection: checkSupabaseConnection
  };
};