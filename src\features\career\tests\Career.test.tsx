import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import Career from '../components/Career';
import store from '../../../app/store';

jest.mock('@/core/i18n/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string, options?: any) => {
      if (key === 'career.positions' && options?.returnObjects) {
        return [
          {
            title: 'Test Position',
            type: 'Full-time',
            duration: '3-6 months',
            tasks: ['Task 1', 'Task 2'],
            requirements: ['Req 1', 'Req 2'],
          },
        ];
      }
      return key;
    },
  }),
}));

const renderWithProviders = (component: React.ReactNode) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('Career Component', () => {
  it('renders career page title', () => {
    renderWithProviders(<Career />);
    expect(screen.getByText(/join our team/i)).toBeInTheDocument();
  });

  it('displays job listings', () => {
    renderWithProviders(<Career />);
    expect(screen.getByText(/property manager/i)).toBeInTheDocument();
    expect(screen.getByText(/real estate agent/i)).toBeInTheDocument();
    expect(screen.getByText(/marketing specialist/i)).toBeInTheDocument();
  });

  it('shows benefits section', () => {
    renderWithProviders(<Career />);
    expect(screen.getByText(/why join us/i)).toBeInTheDocument();
    expect(screen.getByText(/growth opportunities/i)).toBeInTheDocument();
    expect(screen.getByText(/competitive package/i)).toBeInTheDocument();
    expect(screen.getByText(/great culture/i)).toBeInTheDocument();
  });

  it('renders apply buttons for each job listing', () => {
    renderWithProviders(<Career />);
    const applyButtons = screen.getAllByText(/apply now/i);
    expect(applyButtons.length).toBeGreaterThan(0);
  });

  it('renders career positions', () => {
    renderWithProviders(<Career />);
    
    expect(screen.getByText('Test Position')).toBeInTheDocument();
    expect(screen.getByText('Full-time')).toBeInTheDocument();
    expect(screen.getByText('3-6 months')).toBeInTheDocument();
    expect(screen.getByText('Task 1')).toBeInTheDocument();
    expect(screen.getByText('Task 2')).toBeInTheDocument();
    expect(screen.getByText('Req 1')).toBeInTheDocument();
    expect(screen.getByText('Req 2')).toBeInTheDocument();
  });

  it('shows application form when apply button is clicked', () => {
    renderWithProviders(<Career />);
    
    // Click apply button
    fireEvent.click(screen.getByText('career.apply'));
    
    // Check if application form is shown
    expect(screen.getByText(/career.application.title/)).toBeInTheDocument();
  });

  it('returns to positions list when back button is clicked', () => {
    renderWithProviders(<Career />);
    
    // Click apply button
    fireEvent.click(screen.getByText('career.apply'));
    
    // Click back button
    fireEvent.click(screen.getByText('common.back'));
    
    // Check if we're back to the positions list
    expect(screen.getByText('Test Position')).toBeInTheDocument();
  });
});
