import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { fr, enUS } from 'date-fns/locale';
import { useApplications } from '../hooks/useApplications';

const Applications: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { applications, loading, error, updateApplicationStatus } = useApplications();
  const [updatingId, setUpdatingId] = useState<string | null>(null);
  const dateLocale = i18n.language === 'fr' ? fr : enUS;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-italian-green"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900 p-4 rounded-md">
        <p className="text-red-800 dark:text-red-200">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          {t('admin.applications.title')}
        </h1>
      </div>

      {applications.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">{t('admin.applications.list.empty')}</p>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.applications.table.name')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.applications.table.email')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.applications.table.position')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.applications.table.status')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.applications.table.date')}
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.applications.table.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {applications.map((application) => (
                <tr key={application.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {`${application.first_name} ${application.last_name}`}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {application.email}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{application.career_title}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <select
                        className={`text-sm rounded-md ${updatingId === application.id ? 'bg-blue-50 dark:bg-blue-900' : 'bg-white dark:bg-gray-700'} border border-gray-300 dark:border-gray-600`}
                        value={application.status || 'pending'}
                        disabled={updatingId === application.id}
                        onChange={async (e) => {
                          try {
                            setUpdatingId(application.id);
                            await updateApplicationStatus(application.id, e.target.value);
                          } finally {
                            setUpdatingId(null);
                          }
                        }}
                      >
                        <option value="pending">{t('admin.applications.status.pending')}</option>
                        <option value="reviewed">{t('admin.applications.status.reviewed')}</option>
                        <option value="contacted">{t('admin.applications.status.contacted')}</option>
                        <option value="rejected">{t('admin.applications.status.rejected')}</option>
                      </select>
                      {updatingId === application.id && (
                        <div className="ml-2 animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-italian-green"></div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {format(new Date(application.created_at), 'PPp', { locale: dateLocale })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-4"
                      onClick={() => window.open(application.resume_url, '_blank')}
                    >
                      {t('admin.applications.actions.download')}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default Applications;
