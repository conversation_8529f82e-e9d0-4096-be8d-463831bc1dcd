import{u as t,j as e}from"./index-aYgmXB_2.js";const r=()=>{const{t:s}=t();return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsx("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:s("admin.properties.title")})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:s("admin.properties.comingSoon")})})]})};export{r as default};
