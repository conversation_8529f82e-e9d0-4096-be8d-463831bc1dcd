# Project Architecture Diagrams

## Component Structure
```mermaid
graph TD
    A[App] --> B[Core]
    A --> C[Features]
    B --> D[Navigation]
    B --> E[Theme]
    B --> F[Language]
    B --> G[State Management]
    C --> H[Home]
    C --> I[Properties]
    C --> J[Services]
    C --> K[About]
    
    %% Core Details
    D --> D1[Header]
    D --> D2[Footer]
    E --> E1[Light/Dark Theme]
    E --> E2[Italian/Moroccan Colors]
    F --> F1[EN/FR Translation]
    G --> G1[Global State]
    
    %% Feature Details
    H --> H1[Banner]
    H --> H2[Featured Properties]
    I --> I1[Property List]
    I --> I2[Property Details]
    I --> I3[Property Filters]
    J --> J1[Service List]
    K --> K1[Company Info]
    K --> K2[CEO Section]
```

## State Management Flow
```mermaid
graph LR
    A[Actions] --> B[Redux Store]
    B --> C[Reducers]
    C --> D[State]
    D --> E[Components]
    E --> A
```

## Data Flow
```mermaid
flowchart TD
    A[User Input] --> B[Components]
    B --> C[Actions]
    C --> D[Redux Store]
    D --> E[State Update]
    E --> F[UI Update]
    F --> A
