import { RouteObject } from 'react-router-dom';
import { AdminLayout } from './components/AdminLayout';
import { ApplicationsPage } from './pages/ApplicationsPage';
import { ContactsPage } from './pages/ContactsPage';

export const adminRoutes: RouteObject[] = [
  {
    path: '/admin',
    element: <AdminLayout />,
    children: [
      {
        path: 'applications',
        element: <ApplicationsPage />,
      },
      {
        path: 'contacts',
        element: <ContactsPage />,
      },
      {
        path: '',
        // Redirect to applications by default
        element: <ApplicationsPage />,
      },
    ],
  },
];
