import{r as P,s as q,P as V}from"./index-aYgmXB_2.js";const Q=6048e5,G=864e5,N=Symbol.for("constructDateFrom");function v(n,e){return typeof n=="function"?n(e):n&&typeof n=="object"&&N in n?n[N](e):n instanceof Date?new n.constructor(e):new Date(e)}function g(n,e){return v(e||n,n)}let I={};function T(){return I}function x(n,e){var o,u,d,m;const t=T(),r=(e==null?void 0:e.weekStartsOn)??((u=(o=e==null?void 0:e.locale)==null?void 0:o.options)==null?void 0:u.weekStartsOn)??t.weekStartsOn??((m=(d=t.locale)==null?void 0:d.options)==null?void 0:m.weekStartsOn)??0,a=g(n,e==null?void 0:e.in),s=a.getDay(),i=(s<r?7:0)+s-r;return a.setDate(a.getDate()-i),a.setHours(0,0,0,0),a}function S(n,e){return x(n,{...e,weekStartsOn:1})}function p(n,e){const t=g(n,e==null?void 0:e.in),r=t.getFullYear(),a=v(t,0);a.setFullYear(r+1,0,4),a.setHours(0,0,0,0);const s=S(a),i=v(t,0);i.setFullYear(r,0,4),i.setHours(0,0,0,0);const o=S(i);return t.getTime()>=s.getTime()?r+1:t.getTime()>=o.getTime()?r:r-1}function $(n){const e=g(n),t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),+n-+t}function z(n,...e){const t=v.bind(null,e.find(r=>typeof r=="object"));return e.map(t)}function H(n,e){const t=g(n,e==null?void 0:e.in);return t.setHours(0,0,0,0),t}function U(n,e,t){const[r,a]=z(t==null?void 0:t.in,n,e),s=H(r),i=H(a),o=+s-$(s),u=+i-$(i);return Math.round((o-u)/G)}function K(n,e){const t=p(n,e),r=v(n,0);return r.setFullYear(t,0,4),r.setHours(0,0,0,0),S(r)}function Z(n){return n instanceof Date||typeof n=="object"&&Object.prototype.toString.call(n)==="[object Date]"}function ee(n){return!(!Z(n)&&typeof n!="number"||isNaN(+g(n)))}function te(n,e){const t=g(n,e==null?void 0:e.in);return t.setFullYear(t.getFullYear(),0,1),t.setHours(0,0,0,0),t}const ne={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},re=(n,e,t)=>{let r;const a=ne[n];return typeof a=="string"?r=a:e===1?r=a.one:r=a.other.replace("{{count}}",e.toString()),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?"in "+r:r+" ago":r};function W(n){return(e={})=>{const t=e.width?String(e.width):n.defaultWidth;return n.formats[t]||n.formats[n.defaultWidth]}}const ae={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},ie={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},se={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},oe={date:W({formats:ae,defaultWidth:"full"}),time:W({formats:ie,defaultWidth:"full"}),dateTime:W({formats:se,defaultWidth:"full"})},ue={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},ce=(n,e,t,r)=>ue[n];function w(n){return(e,t)=>{const r=t!=null&&t.context?String(t.context):"standalone";let a;if(r==="formatting"&&n.formattingValues){const i=n.defaultFormattingWidth||n.defaultWidth,o=t!=null&&t.width?String(t.width):i;a=n.formattingValues[o]||n.formattingValues[i]}else{const i=n.defaultWidth,o=t!=null&&t.width?String(t.width):n.defaultWidth;a=n.values[o]||n.values[i]}const s=n.argumentCallback?n.argumentCallback(e):e;return a[s]}}const de={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},me={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},le={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},he={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},fe={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},ge={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},we=(n,e)=>{const t=Number(n),r=t%100;if(r>20||r<10)switch(r%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},ye={ordinalNumber:we,era:w({values:de,defaultWidth:"wide"}),quarter:w({values:me,defaultWidth:"wide",argumentCallback:n=>n-1}),month:w({values:le,defaultWidth:"wide"}),day:w({values:he,defaultWidth:"wide"}),dayPeriod:w({values:fe,defaultWidth:"wide",formattingValues:ge,defaultFormattingWidth:"wide"})};function y(n){return(e,t={})=>{const r=t.width,a=r&&n.matchPatterns[r]||n.matchPatterns[n.defaultMatchWidth],s=e.match(a);if(!s)return null;const i=s[0],o=r&&n.parsePatterns[r]||n.parsePatterns[n.defaultParseWidth],u=Array.isArray(o)?ve(o,f=>f.test(i)):be(o,f=>f.test(i));let d;d=n.valueCallback?n.valueCallback(u):u,d=t.valueCallback?t.valueCallback(d):d;const m=e.slice(i.length);return{value:d,rest:m}}}function be(n,e){for(const t in n)if(Object.prototype.hasOwnProperty.call(n,t)&&e(n[t]))return t}function ve(n,e){for(let t=0;t<n.length;t++)if(e(n[t]))return t}function A(n){return(e,t={})=>{const r=e.match(n.matchPattern);if(!r)return null;const a=r[0],s=e.match(n.parsePattern);if(!s)return null;let i=n.valueCallback?n.valueCallback(s[0]):s[0];i=t.valueCallback?t.valueCallback(i):i;const o=e.slice(a.length);return{value:i,rest:o}}}const Me=/^(\d+)(th|st|nd|rd)?/i,Pe=/\d+/i,ke={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},We={any:[/^b/i,/^(a|c)/i]},Oe={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},De={any:[/1/i,/2/i,/3/i,/4/i]},xe={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Se={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Te={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Ye={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Ce={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Ee={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},je={ordinalNumber:A({matchPattern:Me,parsePattern:Pe,valueCallback:n=>parseInt(n,10)}),era:y({matchPatterns:ke,defaultMatchWidth:"wide",parsePatterns:We,defaultParseWidth:"any"}),quarter:y({matchPatterns:Oe,defaultMatchWidth:"wide",parsePatterns:De,defaultParseWidth:"any",valueCallback:n=>n+1}),month:y({matchPatterns:xe,defaultMatchWidth:"wide",parsePatterns:Se,defaultParseWidth:"any"}),day:y({matchPatterns:Te,defaultMatchWidth:"wide",parsePatterns:Ye,defaultParseWidth:"any"}),dayPeriod:y({matchPatterns:Ce,defaultMatchWidth:"any",parsePatterns:Ee,defaultParseWidth:"any"})},Fe={code:"en-US",formatDistance:re,formatLong:oe,formatRelative:ce,localize:ye,match:je,options:{weekStartsOn:0,firstWeekContainsDate:1}};function qe(n,e){const t=g(n,e==null?void 0:e.in);return U(t,te(t))+1}function Ne(n,e){const t=g(n,e==null?void 0:e.in),r=+S(t)-+K(t);return Math.round(r/Q)+1}function R(n,e){var m,f,O,D;const t=g(n,e==null?void 0:e.in),r=t.getFullYear(),a=T(),s=(e==null?void 0:e.firstWeekContainsDate)??((f=(m=e==null?void 0:e.locale)==null?void 0:m.options)==null?void 0:f.firstWeekContainsDate)??a.firstWeekContainsDate??((D=(O=a.locale)==null?void 0:O.options)==null?void 0:D.firstWeekContainsDate)??1,i=v((e==null?void 0:e.in)||n,0);i.setFullYear(r+1,0,s),i.setHours(0,0,0,0);const o=x(i,e),u=v((e==null?void 0:e.in)||n,0);u.setFullYear(r,0,s),u.setHours(0,0,0,0);const d=x(u,e);return+t>=+o?r+1:+t>=+d?r:r-1}function $e(n,e){var o,u,d,m;const t=T(),r=(e==null?void 0:e.firstWeekContainsDate)??((u=(o=e==null?void 0:e.locale)==null?void 0:o.options)==null?void 0:u.firstWeekContainsDate)??t.firstWeekContainsDate??((m=(d=t.locale)==null?void 0:d.options)==null?void 0:m.firstWeekContainsDate)??1,a=R(n,e),s=v((e==null?void 0:e.in)||n,0);return s.setFullYear(a,0,r),s.setHours(0,0,0,0),x(s,e)}function He(n,e){const t=g(n,e==null?void 0:e.in),r=+x(t,e)-+$e(t,e);return Math.round(r/Q)+1}function c(n,e){const t=n<0?"-":"",r=Math.abs(n).toString().padStart(e,"0");return t+r}const b={y(n,e){const t=n.getFullYear(),r=t>0?t:1-t;return c(e==="yy"?r%100:r,e.length)},M(n,e){const t=n.getMonth();return e==="M"?String(t+1):c(t+1,2)},d(n,e){return c(n.getDate(),e.length)},a(n,e){const t=n.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];case"aaaa":default:return t==="am"?"a.m.":"p.m."}},h(n,e){return c(n.getHours()%12||12,e.length)},H(n,e){return c(n.getHours(),e.length)},m(n,e){return c(n.getMinutes(),e.length)},s(n,e){return c(n.getSeconds(),e.length)},S(n,e){const t=e.length,r=n.getMilliseconds(),a=Math.trunc(r*Math.pow(10,t-3));return c(a,e.length)}},k={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},J={G:function(n,e,t){const r=n.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return t.era(r,{width:"abbreviated"});case"GGGGG":return t.era(r,{width:"narrow"});case"GGGG":default:return t.era(r,{width:"wide"})}},y:function(n,e,t){if(e==="yo"){const r=n.getFullYear(),a=r>0?r:1-r;return t.ordinalNumber(a,{unit:"year"})}return b.y(n,e)},Y:function(n,e,t,r){const a=R(n,r),s=a>0?a:1-a;if(e==="YY"){const i=s%100;return c(i,2)}return e==="Yo"?t.ordinalNumber(s,{unit:"year"}):c(s,e.length)},R:function(n,e){const t=p(n);return c(t,e.length)},u:function(n,e){const t=n.getFullYear();return c(t,e.length)},Q:function(n,e,t){const r=Math.ceil((n.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return c(r,2);case"Qo":return t.ordinalNumber(r,{unit:"quarter"});case"QQQ":return t.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return t.quarter(r,{width:"wide",context:"formatting"})}},q:function(n,e,t){const r=Math.ceil((n.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return c(r,2);case"qo":return t.ordinalNumber(r,{unit:"quarter"});case"qqq":return t.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return t.quarter(r,{width:"wide",context:"standalone"})}},M:function(n,e,t){const r=n.getMonth();switch(e){case"M":case"MM":return b.M(n,e);case"Mo":return t.ordinalNumber(r+1,{unit:"month"});case"MMM":return t.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return t.month(r,{width:"wide",context:"formatting"})}},L:function(n,e,t){const r=n.getMonth();switch(e){case"L":return String(r+1);case"LL":return c(r+1,2);case"Lo":return t.ordinalNumber(r+1,{unit:"month"});case"LLL":return t.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return t.month(r,{width:"wide",context:"standalone"})}},w:function(n,e,t,r){const a=He(n,r);return e==="wo"?t.ordinalNumber(a,{unit:"week"}):c(a,e.length)},I:function(n,e,t){const r=Ne(n);return e==="Io"?t.ordinalNumber(r,{unit:"week"}):c(r,e.length)},d:function(n,e,t){return e==="do"?t.ordinalNumber(n.getDate(),{unit:"date"}):b.d(n,e)},D:function(n,e,t){const r=qe(n);return e==="Do"?t.ordinalNumber(r,{unit:"dayOfYear"}):c(r,e.length)},E:function(n,e,t){const r=n.getDay();switch(e){case"E":case"EE":case"EEE":return t.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(r,{width:"short",context:"formatting"});case"EEEE":default:return t.day(r,{width:"wide",context:"formatting"})}},e:function(n,e,t,r){const a=n.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(s);case"ee":return c(s,2);case"eo":return t.ordinalNumber(s,{unit:"day"});case"eee":return t.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(a,{width:"short",context:"formatting"});case"eeee":default:return t.day(a,{width:"wide",context:"formatting"})}},c:function(n,e,t,r){const a=n.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(s);case"cc":return c(s,e.length);case"co":return t.ordinalNumber(s,{unit:"day"});case"ccc":return t.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(a,{width:"narrow",context:"standalone"});case"cccccc":return t.day(a,{width:"short",context:"standalone"});case"cccc":default:return t.day(a,{width:"wide",context:"standalone"})}},i:function(n,e,t){const r=n.getDay(),a=r===0?7:r;switch(e){case"i":return String(a);case"ii":return c(a,e.length);case"io":return t.ordinalNumber(a,{unit:"day"});case"iii":return t.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(r,{width:"short",context:"formatting"});case"iiii":default:return t.day(r,{width:"wide",context:"formatting"})}},a:function(n,e,t){const a=n.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(n,e,t){const r=n.getHours();let a;switch(r===12?a=k.noon:r===0?a=k.midnight:a=r/12>=1?"pm":"am",e){case"b":case"bb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(n,e,t){const r=n.getHours();let a;switch(r>=17?a=k.evening:r>=12?a=k.afternoon:r>=4?a=k.morning:a=k.night,e){case"B":case"BB":case"BBB":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(n,e,t){if(e==="ho"){let r=n.getHours()%12;return r===0&&(r=12),t.ordinalNumber(r,{unit:"hour"})}return b.h(n,e)},H:function(n,e,t){return e==="Ho"?t.ordinalNumber(n.getHours(),{unit:"hour"}):b.H(n,e)},K:function(n,e,t){const r=n.getHours()%12;return e==="Ko"?t.ordinalNumber(r,{unit:"hour"}):c(r,e.length)},k:function(n,e,t){let r=n.getHours();return r===0&&(r=24),e==="ko"?t.ordinalNumber(r,{unit:"hour"}):c(r,e.length)},m:function(n,e,t){return e==="mo"?t.ordinalNumber(n.getMinutes(),{unit:"minute"}):b.m(n,e)},s:function(n,e,t){return e==="so"?t.ordinalNumber(n.getSeconds(),{unit:"second"}):b.s(n,e)},S:function(n,e){return b.S(n,e)},X:function(n,e,t){const r=n.getTimezoneOffset();if(r===0)return"Z";switch(e){case"X":return L(r);case"XXXX":case"XX":return M(r);case"XXXXX":case"XXX":default:return M(r,":")}},x:function(n,e,t){const r=n.getTimezoneOffset();switch(e){case"x":return L(r);case"xxxx":case"xx":return M(r);case"xxxxx":case"xxx":default:return M(r,":")}},O:function(n,e,t){const r=n.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+X(r,":");case"OOOO":default:return"GMT"+M(r,":")}},z:function(n,e,t){const r=n.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+X(r,":");case"zzzz":default:return"GMT"+M(r,":")}},t:function(n,e,t){const r=Math.trunc(+n/1e3);return c(r,e.length)},T:function(n,e,t){return c(+n,e.length)}};function X(n,e=""){const t=n>0?"-":"+",r=Math.abs(n),a=Math.trunc(r/60),s=r%60;return s===0?t+String(a):t+String(a)+e+c(s,2)}function L(n,e){return n%60===0?(n>0?"-":"+")+c(Math.abs(n)/60,2):M(n,e)}function M(n,e=""){const t=n>0?"-":"+",r=Math.abs(n),a=c(Math.trunc(r/60),2),s=c(r%60,2);return t+a+e+s}const _=(n,e)=>{switch(n){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},B=(n,e)=>{switch(n){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},Je=(n,e)=>{const t=n.match(/(P+)(p+)?/)||[],r=t[1],a=t[2];if(!a)return _(n,e);let s;switch(r){case"P":s=e.dateTime({width:"short"});break;case"PP":s=e.dateTime({width:"medium"});break;case"PPP":s=e.dateTime({width:"long"});break;case"PPPP":default:s=e.dateTime({width:"full"});break}return s.replace("{{date}}",_(r,e)).replace("{{time}}",B(a,e))},Xe={p:B,P:Je},Le=/^D+$/,_e=/^Y+$/,Qe=["D","DD","YY","YYYY"];function pe(n){return Le.test(n)}function Ae(n){return _e.test(n)}function Re(n,e,t){const r=Be(n,e,t);if(console.warn(r),Qe.includes(n))throw new RangeError(r)}function Be(n,e,t){const r=n[0]==="Y"?"years":"days of the month";return`Use \`${n.toLowerCase()}\` instead of \`${n}\` (in \`${e}\`) for formatting ${r} to the input \`${t}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Ve=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Ge=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Ie=/^'([^]*?)'?$/,ze=/''/g,Ue=/[a-zA-Z]/;function Yt(n,e,t){var m,f,O,D,C,E,j,F;const r=T(),a=(t==null?void 0:t.locale)??r.locale??Fe,s=(t==null?void 0:t.firstWeekContainsDate)??((f=(m=t==null?void 0:t.locale)==null?void 0:m.options)==null?void 0:f.firstWeekContainsDate)??r.firstWeekContainsDate??((D=(O=r.locale)==null?void 0:O.options)==null?void 0:D.firstWeekContainsDate)??1,i=(t==null?void 0:t.weekStartsOn)??((E=(C=t==null?void 0:t.locale)==null?void 0:C.options)==null?void 0:E.weekStartsOn)??r.weekStartsOn??((F=(j=r.locale)==null?void 0:j.options)==null?void 0:F.weekStartsOn)??0,o=g(n,t==null?void 0:t.in);if(!ee(o))throw new RangeError("Invalid time value");let u=e.match(Ge).map(h=>{const l=h[0];if(l==="p"||l==="P"){const Y=Xe[l];return Y(h,a.formatLong)}return h}).join("").match(Ve).map(h=>{if(h==="''")return{isToken:!1,value:"'"};const l=h[0];if(l==="'")return{isToken:!1,value:Ke(h)};if(J[l])return{isToken:!0,value:h};if(l.match(Ue))throw new RangeError("Format string contains an unescaped latin alphabet character `"+l+"`");return{isToken:!1,value:h}});a.localize.preprocessor&&(u=a.localize.preprocessor(o,u));const d={firstWeekContainsDate:s,weekStartsOn:i,locale:a};return u.map(h=>{if(!h.isToken)return h.value;const l=h.value;(!(t!=null&&t.useAdditionalWeekYearTokens)&&Ae(l)||!(t!=null&&t.useAdditionalDayOfYearTokens)&&pe(l))&&Re(l,e,String(n));const Y=J[l[0]];return Y(o,l,a.localize,d)}).join("")}function Ke(n){const e=n.match(Ie);return e?e[1].replace(ze,"'"):n}const Ze={lessThanXSeconds:{one:"moins d’une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d’une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d’un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu’un an",other:"presque {{count}} ans"}},et=(n,e,t)=>{let r;const a=Ze[n];return typeof a=="string"?r=a:e===1?r=a.one:r=a.other.replace("{{count}}",String(e)),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?"dans "+r:"il y a "+r:r},tt={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},nt={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},rt={full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},at={date:W({formats:tt,defaultWidth:"full"}),time:W({formats:nt,defaultWidth:"full"}),dateTime:W({formats:rt,defaultWidth:"full"})},it={lastWeek:"eeee 'dernier à' p",yesterday:"'hier à' p",today:"'aujourd’hui à' p",tomorrow:"'demain à' p'",nextWeek:"eeee 'prochain à' p",other:"P"},st=(n,e,t,r)=>it[n],ot={narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant Jésus-Christ","après Jésus-Christ"]},ut={narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2ème trim.","3ème trim.","4ème trim."],wide:["1er trimestre","2ème trimestre","3ème trimestre","4ème trimestre"]},ct={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],wide:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"]},dt={narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},mt={narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"après-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l’après-midi",evening:"du soir",night:"du matin"}},lt=(n,e)=>{const t=Number(n),r=e==null?void 0:e.unit;if(t===0)return"0";const a=["year","week","hour","minute","second"];let s;return t===1?s=r&&a.includes(r)?"ère":"er":s="ème",t+s},ht=["MMM","MMMM"],ft={preprocessor:(n,e)=>n.getDate()===1||!e.some(r=>r.isToken&&ht.includes(r.value))?e:e.map(r=>r.isToken&&r.value==="do"?{isToken:!0,value:"d"}:r),ordinalNumber:lt,era:w({values:ot,defaultWidth:"wide"}),quarter:w({values:ut,defaultWidth:"wide",argumentCallback:n=>n-1}),month:w({values:ct,defaultWidth:"wide"}),day:w({values:dt,defaultWidth:"wide"}),dayPeriod:w({values:mt,defaultWidth:"wide"})},gt=/^(\d+)(ième|ère|ème|er|e)?/i,wt=/\d+/i,yt={narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},bt={any:[/^av/i,/^ap/i]},vt={narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},Mt={any:[/1/i,/2/i,/3/i,/4/i]},Pt={narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},kt={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},Wt={narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},Ot={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},Dt={narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},xt={any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},St={ordinalNumber:A({matchPattern:gt,parsePattern:wt,valueCallback:n=>parseInt(n)}),era:y({matchPatterns:yt,defaultMatchWidth:"wide",parsePatterns:bt,defaultParseWidth:"any"}),quarter:y({matchPatterns:vt,defaultMatchWidth:"wide",parsePatterns:Mt,defaultParseWidth:"any",valueCallback:n=>n+1}),month:y({matchPatterns:Pt,defaultMatchWidth:"wide",parsePatterns:kt,defaultParseWidth:"any"}),day:y({matchPatterns:Wt,defaultMatchWidth:"wide",parsePatterns:Ot,defaultParseWidth:"any"}),dayPeriod:y({matchPatterns:Dt,defaultMatchWidth:"any",parsePatterns:xt,defaultParseWidth:"any"})},Ct={code:"fr",formatDistance:et,formatLong:at,formatRelative:st,localize:ft,match:St,options:{weekStartsOn:1,firstWeekContainsDate:4}};function Et(n){const e=P.useRef(n),[t,r]=P.useState({data:[],loading:!0,error:null,lastFetched:null}),a=P.useCallback(async()=>{r(i=>({...i,loading:!0}));try{let i=q.from(e.current.table).select(e.current.select||"*");e.current.filter&&e.current.filter.forEach(({column:d,operator:m,value:f})=>{i=i.filter(d,m,f)}),e.current.orderBy&&(i=i.order(e.current.orderBy.column,{ascending:e.current.orderBy.ascending??!1})),e.current.limit&&(i=i.limit(e.current.limit));const{data:o,error:u}=await i;if(u)throw u;if(!o)throw new Error("No data received");r({data:o,loading:!1,error:null,lastFetched:Date.now()})}catch(i){let o=`Error fetching ${e.current.table} data`,u=null;i instanceof V?(o=i.message,u={code:i.code,details:i.details,hint:i.hint,message:i.message}):i instanceof Error&&(o=i.message,u={name:i.name,stack:i.stack,timestamp:new Date().toISOString()}),r(d=>({...d,loading:!1,error:o,errorDetails:u}))}},[]),s=P.useCallback(async(i,o)=>{try{const{error:u}=await q.from(e.current.table).update(o).eq("id",i);if(u)throw u;return r(d=>({...d,data:d.data.map(m=>m.id===i?{...m,...o}:m)})),!0}catch(u){throw u}},[]);return P.useEffect(()=>{e.current=n},[n]),P.useEffect(()=>{a()},[a]),{...t,refetch:a,updateRecord:s}}export{Yt as a,Fe as e,Ct as f,Et as u};
