import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import type { RootState } from '../../../app/store';
import type { Property } from '../../properties/propertiesSlice';
import TypewriterText from '../../contact/components/TypewriterText';

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
}

interface Service {
  icon: string;
  title: string;
  description: string;
  color?: string;
}

interface Stat {
  value: string;
  label: string;
}

const Home: React.FC = () => {
  const { t, i18n } = useTranslation();
  const properties = useSelector((state: RootState) => state.properties.items);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [particles, setParticles] = useState<Particle[]>([]);

  // Stats data
  const stats: Stat[] = [
    { value: '1000+', label: t('about.stats.properties') },
    { value: '500+', label: t('about.stats.clients') },
    { value: '10+', label: t('about.stats.years') },
    { value: '100%', label: t('about.stats.satisfaction') }
  ];

  // Mouse position effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Particle effect
  useEffect(() => {
    const colors = ['#008C45', '#00B358', '#006633', '#66E6A8'];
    const interval = setInterval(() => {
      setParticles(prev => [
        ...prev,
        {
          id: Math.random(),
          x: Math.random() * window.innerWidth,
          y: window.innerHeight,
          size: Math.random() * 6 + 2,
          color: colors[Math.floor(Math.random() * colors.length)]
        }
      ].slice(-30));
    }, 150);

    return () => clearInterval(interval);
  }, []);

  // Animation variants
  const titleRevealAnimation = {
    initial: { 
      opacity: 0,
      clipPath: "polygon(0 0, 0 0, 0 100%, 0% 100%)"
    },
    animate: { 
      opacity: 1,
      clipPath: "polygon(0 0, 100% 0, 100% 100%, 0 100%)",
      transition: {
        duration: 1.2,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const titleGlowAnimation = {
    animate: {
      textShadow: [
        "0 0 0px rgba(0,140,69,0)",
        "0 0 20px rgba(0,140,69,0.4)",
        "0 0 0px rgba(0,140,69,0)"
      ],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const floatingAnimation = {
    y: [0, -20, 0],
    x: [0, 15, -15, 0],
    rotate: [0, 5, -5, 0],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  const glowingAnimation = {
    boxShadow: [
      "0 0 20px rgba(34, 197, 94, 0.3)",
      "0 0 40px rgba(34, 197, 94, 0.5)",
      "0 0 60px rgba(34, 197, 94, 0.3)"
    ],
    scale: [1, 1.05, 1],
    rotate: [0, 2, -2, 0],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  const services: Service[] = [
    {
      icon: "🏢",
      title: t('services.propertyManagement.title'),
      description: t('services.propertyManagement.description'),
      color: "#00B358"
    },
    {
      icon: "📊",
      title: t('services.consulting.title'),
      description: t('services.consulting.description'),
      color: "#008C45"
    },
    {
      icon: "🔑",
      title: t('services.rental.title'),
      description: t('services.rental.description'),
      color: "#66E6A8"
    },
  ];

  return (
    <div className="bg-gray-50 dark:bg-gray-800 overflow-hidden dark:textured-bg">
      {/* Particles */}
      {particles.map((particle: Particle) => (
        <motion.div
          key={particle.id}
          className="fixed rounded-full z-0"
          style={{
            width: particle.size,
            height: particle.size,
            backgroundColor: particle.color,
            filter: 'blur(1px)'
          }}
          initial={{ x: particle.x, y: particle.y, opacity: 0.8 }}
          animate={{
            y: -window.innerHeight,
            x: particle.x + (Math.random() * 200 - 100),
            opacity: 0,
            scale: [1, 1.5, 0],
            rotate: [0, 360]
          }}
          transition={{
            duration: 5 + Math.random() * 3,
            ease: "easeOut"
          }}
        />
      ))}

      {/* Hero Section */}
      <section className="relative min-h-[700px] flex items-center overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Dynamic Gradient Background */}
          <motion.div
            className="absolute inset-0 opacity-20 dark:opacity-30"
            style={{
              background: 'radial-gradient(circle at var(--x) var(--y), transparent 10%, var(--color-primary) 70%)',
              backgroundSize: '200% 200%',
            }}
            animate={{
              '--x': `${mousePosition.x}px`,
              '--y': `${mousePosition.y}px`,
            } as any}
          />
          
          {/* Decorative Elements */}
          <motion.div 
            className="absolute right-0 top-20 w-72 h-72 rounded-full bg-italian-green/5 dark:bg-italian-green/10"
            animate={{
              scale: [1, 1.2, 1],
              x: [0, 10, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <motion.div 
            className="absolute left-20 bottom-20 w-96 h-96 rounded-full bg-italian-green/5 dark:bg-italian-green/10"
            animate={{
              scale: [0.8, 1, 0.8],
              y: [0, -15, 0],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          
          {/* Grid Pattern */}
          <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="max-w-xl"
            >
              <motion.div 
                className="inline-block mb-4 bg-italian-green/10 dark:bg-italian-green/20 px-4 py-2 rounded-full"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <span className="text-italian-green dark:text-italian-green-light font-medium">
                  {t('home.hero.badge') || 'Premier Real Estate Agency'}
                </span>
              </motion.div>
              
              <motion.h1
                className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6 relative heading"
                initial={titleRevealAnimation.initial}
                animate={titleRevealAnimation.animate}
                whileInView={titleGlowAnimation.animate}
                viewport={{ once: false }}
              >
                <motion.span
                  className="absolute inset-0 bg-gradient-to-r from-italian-green/20 to-transparent"
                  animate={{
                    opacity: [0, 0.5, 0],
                    x: ['-100%', '100%'],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                <span className="block">
                  <TypewriterText text={t('home.hero.title') || 'Elevate Your Living Experience'} delay={70} />
                </span>
                <span className="relative inline-block">
                  <span className="relative z-10 text-italian-green dark:text-italian-green-light">
                    {t('home.hero.highlight') || 'Beyond Expectations'}
                  </span>
                  <motion.span 
                    className="absolute bottom-1 left-0 h-3 bg-italian-green/20 dark:bg-italian-green/30 w-full -z-0"
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    transition={{ duration: 1, delay: 1.2 }}
                  />
                </span>
              </motion.h1>
              
              <motion.p
                className="text-xl text-gray-600 dark:text-gray-300 mb-8 paragraph leading-relaxed"
                animate={floatingAnimation}
              >
                <TypewriterText 
                  text={t('home.hero.subtitle') || 'Discover exclusive properties tailored to your lifestyle, with premium locations and exceptional amenities for the discerning buyer.'} 
                  delay={40} 
                />
              </motion.p>
              
              <motion.div className="flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                <motion.div
                  whileHover={{ 
                    scale: 1.05,
                    boxShadow: "0 10px 25px rgba(0, 140, 69, 0.3)",
                  }}
                  whileTap={{ scale: 0.95 }}
                  className="relative overflow-hidden"
                >
                  <motion.span 
                    className="absolute inset-0 bg-gradient-to-r from-italian-green/20 to-transparent"
                    animate={{
                      x: ['-100%', '100%'],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                  <Link
                    to="/properties"
                    className="relative z-10 inline-block w-full sm:w-auto text-center bg-italian-green text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-italian-green-dark transition-colors transform-gpu shadow-lg"
                  >
                    {t('home.hero.cta') || 'Explore Properties'}
                  </Link>
                </motion.div>
                
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    to="/contact"
                    className="inline-block w-full sm:w-auto text-center border-2 border-italian-green text-italian-green dark:text-italian-green-light px-8 py-4 rounded-lg text-lg font-semibold hover:bg-italian-green/10 transition-colors transform-gpu"
                  >
                    {t('home.hero.secondaryCta') || 'Schedule a Viewing'}
                  </Link>
                </motion.div>
              </motion.div>
              
              <motion.div 
                className="mt-8 flex items-center gap-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 1.2 }}
              >
                <div className="flex -space-x-3">
                  {Array(4).fill(0).map((_, i) => (
                    <div key={i} className={`w-10 h-10 rounded-full border-2 border-white bg-gray-${(i + 2) * 100} flex items-center justify-center text-white text-xs font-bold`}>
                      {['JD', 'AR', 'MK', '+'][i]}
                    </div>
                  ))}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-semibold">{t('home.hero.trustBadge') || '2,000+ Happy Clients'}</span>
                  <div className="flex items-center">
                    {Array(5).fill(0).map((_, i) => (
                      <svg key={i} className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                    ))}
                    <span className="ml-1 text-gray-500 dark:text-gray-400">4.9/5</span>
                  </div>
                </div>
              </motion.div>
            </motion.div>
            
            {/* Right Content - Property Showcase */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="hidden lg:block relative"
            >
              <motion.div 
                className="relative z-10 bg-white dark:bg-gray-800 p-4 rounded-xl shadow-2xl overflow-hidden"
                whileHover={{ 
                  y: -10,
                  boxShadow: "0 30px 60px rgba(0,0,0,0.2)",
                  transition: { duration: 0.3 }
                }}
              >
                {properties.length > 0 && (
                  <Link to={`/properties/${properties[0].id}`}>
                    <div className="relative aspect-[4/3] rounded-lg overflow-hidden mb-4">
                      <img 
                        src={properties[0].images[0]} 
                        alt={properties[0].translations[i18n.language as 'en' | 'fr'].title || properties[0].translations.en.title} 
                        className="object-cover w-full h-full transform hover:scale-105 transition-transform duration-700"
                      />
                      <div className="absolute top-3 left-3 bg-italian-green text-white text-sm font-semibold px-3 py-1 rounded-full">
                        {t('properties.featured')}
                      </div>
                    </div>
                    <div className="p-2">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 truncate">
                        {properties[0].translations[i18n.language as 'en' | 'fr'].title}
                      </h3>
                      <div className="flex items-center text-gray-600 dark:text-gray-300 mb-3">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span className="text-sm">{properties[0].location.city}, {properties[0].location.area}</span>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 sm:justify-between">
                        <div className="text-base sm:text-lg font-bold text-italian-green dark:text-italian-green-light">
                          {t('common.currency', { amount: properties[0].price.toLocaleString() })}
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-1 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            <span className="text-sm text-gray-700 dark:text-gray-300">{properties[0].type === 'studio' ? 1 : 2} {t('properties.beds', 'Beds')}</span>
                          </div>
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-1 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span className="text-sm text-gray-700 dark:text-gray-300">{properties[0].type === 'studio' ? 1 : 2} {t('properties.baths', 'Baths')}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                )}
              </motion.div>
              
              {/* Decorative elements */}
              <motion.div
                className="absolute -top-6 -right-6 w-24 h-24 bg-italian-green/20 rounded-full blur-xl"
                animate={{ 
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0.8, 0.5],
                }}
                transition={{ 
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <motion.div
                className="absolute -bottom-10 -left-10 w-32 h-32 bg-italian-green/20 rounded-full blur-xl"
                animate={{ 
                  scale: [1.2, 1, 1.2],
                  opacity: [0.5, 0.7, 0.5],
                }}
                transition={{ 
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            </motion.div>
          </div>
        </div>
        
        {/* Scroll Indicator */}
        <motion.div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ 
            y: [0, 10, 0],
            opacity: [0.3, 1, 0.3]
          }}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <svg className="w-6 h-6 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </motion.div>
      </section>

      {/* Featured Properties */}
      <section className="py-16 bg-white dark:bg-gray-700 dark:bg-opacity-30">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 heading">
              {t('home.featured.title')}
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {properties.slice(0, 3).map((property: Property, index: number) => (
              <motion.div
                key={property.id}
                className="relative z-10 bg-white dark:bg-gray-800 p-4 rounded-xl shadow-2xl overflow-hidden"
                whileHover={{ 
                  y: -10,
                  boxShadow: "0 30px 60px rgba(0,0,0,0.2)",
                  transition: { duration: 0.3 }
                }}
              >
                <div className="relative aspect-[4/3] rounded-lg overflow-hidden mb-4">
                  <img 
                    src={property.images[0]} 
                    alt={property.translations[i18n.language as 'en' | 'fr'].title} 
                    className="object-cover w-full h-full transform hover:scale-105 transition-transform duration-700"
                  />
                  <div className="absolute top-3 left-3 bg-italian-green text-white text-sm font-semibold px-3 py-1 rounded-full">
                    {t('properties.featured')}
                  </div>
                </div>
                <div className="p-2">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 truncate">
                    {property.translations[i18n.language as 'en' | 'fr'].title}
                  </h3>
                  <div className="flex items-center text-gray-600 dark:text-gray-300 mb-3">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span className="text-sm">{property.location.city}, {property.location.area}</span>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 sm:justify-between">
                    <div className="text-base sm:text-lg font-bold text-italian-green dark:text-italian-green-light">
                      {t('common.currency', { amount: property.price.toLocaleString() })}
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        <span className="text-sm text-gray-700 dark:text-gray-300">{property.type === 'studio' ? 1 : 2} {t('properties.beds', 'Beds')}</span>
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span className="text-sm text-gray-700 dark:text-gray-300">{property.type === 'studio' ? 1 : 2} {t('properties.baths', 'Baths')}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800 dark:bg-opacity-40">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4 heading">
              {t('services.title')}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 paragraph max-w-2xl mx-auto">
              {t('services.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service: Service, index: number) => (
              <motion.div
                key={index}
                className="card dark:layered-card transform-gpu p-8 relative hover:border-italian-green hover:border-opacity-40 border-2 border-transparent"
                style={{ 
                  perspective: "1000px",
                  boxShadow: `0 10px 30px rgba(0, 140, 69, 0.1)` 
                }}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                whileHover={{ 
                  scale: 1.03,
                  boxShadow: `0 20px 40px rgba(0, 140, 69, 0.2)`,
                  transition: { duration: 0.3 }
                }}
              >
                <motion.div 
                  className="absolute inset-0 bg-gradient-to-br from-italian-green/5 to-transparent rounded-xl"
                  animate={{ 
                    opacity: [0.5, 0.8, 0.5] 
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <div className="relative z-10">
                  <motion.div 
                    className="text-5xl mb-6 inline-flex items-center justify-center w-16 h-16 rounded-full"
                    style={{ 
                      background: `linear-gradient(135deg, ${service.color || '#008C45'} 0%, rgba(0,179,88,0.2) 100%)`,
                      color: '#fff',
                      boxShadow: `0 8px 20px rgba(0, 140, 69, 0.2)`
                    }}
                    animate={{ 
                      y: [0, -8, 0],
                      scale: [1, 1.1, 1]
                    }}
                    transition={{ 
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: index * 0.3
                    }}
                  >
                    {service.icon}
                  </motion.div>
                  <motion.h3 
                    className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 heading"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ 
                      opacity: 1, 
                      y: 0,
                      transition: {
                        duration: 0.8,
                        ease: [0.22, 1, 0.36, 1]
                      }
                    }}
                    viewport={{ once: true }}
                  >
                    {service.title}
                  </motion.h3>
                  <p className="text-gray-600 dark:text-gray-300 paragraph leading-relaxed">
                    {service.description}
                  </p>
                  
                  <motion.div 
                    className="mt-6 flex justify-end"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.3 + index * 0.2 }}
                  >
                    <Link
                      to="/services"
                      className="text-italian-green hover:text-italian-green-dark dark:text-italian-green-light flex items-center group"
                    >
                      <span className="mr-2">{t('common.learnMore')}</span>
                      <motion.span
                        animate={{ x: [0, 5, 0] }}
                        transition={{ 
                          duration: 1.5, 
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: index * 0.2
                        }}
                        className="group-hover:translate-x-2 transition-transform"
                      >
                        →
                      </motion.span>
                    </Link>
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-italian-green bg-opacity-90 text-white relative overflow-hidden">
        <motion.div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at center, transparent 30%, rgba(0,0,0,0.2) 100%)',
          }}
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 0.8, 0.5],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        <motion.div 
          className="absolute inset-0"
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.1\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '30px 30px',
          }}
        />
        
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="text-center mb-10"
            initial={{ opacity: 0, y: -30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-2 text-white heading">
              {t('about.stats.title')}
            </h2>
            <p className="text-lg text-white text-opacity-80 max-w-2xl mx-auto paragraph">
              {t('about.stats.subtitle')}
            </p>
          </motion.div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="text-center backdrop-blur-sm bg-white/10 rounded-xl p-6 shadow-lg"
                initial={{ opacity: 0, scale: 0.5, y: 20 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: "0 10px 25px rgba(0,0,0,0.2)"
                }}
              >
                <motion.div 
                  className="text-5xl font-bold mb-3"
                  animate={{ 
                    scale: [1, 1.2, 1],
                    rotate: [0, 3, -3, 0],
                    textShadow: [
                      "0 0 0px rgba(255,255,255,0.5)",
                      "0 0 20px rgba(255,255,255,0.8)",
                      "0 0 0px rgba(255,255,255,0.5)"
                    ]
                  }}
                  transition={{ 
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: index * 0.2
                  }}
                >
                  {stat.value}
                </motion.div>
                <motion.div 
                  className="text-sm font-medium uppercase tracking-wider opacity-90"
                  animate={{ 
                    y: [0, -3, 0],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: index * 0.1
                  }}
                >
                  {stat.label}
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800 dark:bg-opacity-40">
        <div className="container mx-auto px-4">
          <motion.div
            className="card dark:layered-card p-8 md:p-12 text-center max-w-4xl mx-auto shadow-xl relative overflow-hidden"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            whileHover={{ 
              scale: 1.02,
              boxShadow: "0 20px 40px rgba(0,0,0,0.2)"
            }}
          >
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-italian-green/10 to-transparent"
              animate={{
                x: ['-100%', '100%'],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.h2 
              className="text-3xl font-bold text-gray-900 dark:text-white mb-4 relative z-10 heading"
              initial={titleRevealAnimation.initial}
              whileInView={titleRevealAnimation.animate}
              viewport={{ once: true }}
            >
              {t('common.welcome')}
            </motion.h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 relative z-10 paragraph">
              {t('footer.description')}
            </p>
            <motion.div
              className="relative z-10"
              whileHover={{ 
                scale: 1.1,
                rotate: [0, -2, 2, 0],
              }}
              whileTap={{ scale: 0.9 }}
              drag
              dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
              dragElastic={0.1}
            >
              <Link
                to="/contact"
                className="inline-block bg-italian-green text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-italian-green/90 transition-colors transform-gpu"
              >
                {t('common.contact')}
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
