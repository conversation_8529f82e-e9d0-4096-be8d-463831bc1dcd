import React from 'react';
import { Route, Routes, Navigate } from 'react-router-dom';
import Home from '../features/home/<USER>/Home.tsx';
import Properties from '../features/properties/components/Properties.tsx';
import PropertyDetail from '../features/properties/components/PropertyDetail.tsx';
import Services from '../features/services/components/Services.tsx';
import About from '../features/about/components/About.tsx';
import Contact from '../features/contact/components/Contact.tsx';
import Career from '../features/career/components/Career.tsx';
import ApplicationForm from '../features/career/components/ApplicationForm.tsx';
import ThankYou from '../features/career/components/ThankYou.tsx';
import { Terms } from '../features/terms/components/Terms.tsx';
import { Privacy } from '../features/privacy/components/Privacy.tsx';
import Login from '../features/admin/pages/Login.tsx';
import AdminLayout from '../features/admin/layouts/AdminLayout.tsx';
import ProtectedRoute from '../features/admin/components/ProtectedRoute.tsx';

// Lazy load admin pages
const Dashboard = React.lazy(() => import('../features/admin/pages/Dashboard.tsx'));
const Applications = React.lazy(() => import('../features/admin/pages/Applications.tsx'));
const Contacts = React.lazy(() => import('../features/admin/pages/Contacts.tsx'));
const Careers = React.lazy(() => import('../features/admin/pages/Careers.tsx'));
const PropertiesAdmin = React.lazy(() => import('../features/admin/pages/Properties.tsx'));

// Admin route wrapper component
const AdminRoute: React.FC<{ element: React.ReactNode }> = ({ element }) => (
  <ProtectedRoute>
    <AdminLayout>
      <React.Suspense fallback={<div>Loading...</div>}>
        {element}
      </React.Suspense>
    </AdminLayout>
  </ProtectedRoute>
);

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/" element={<Home />} />
      <Route path="/properties" element={<Properties />} />
      <Route path="/properties/:id" element={<PropertyDetail />} />
      <Route path="/services" element={<Services />} />
      <Route path="/about" element={<About />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/career" element={<Career />} />
      <Route path="/career/apply/:positionId" element={<ApplicationForm />} />
      <Route path="/career/thank-you" element={<ThankYou />} />
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<Privacy />} />
      
      {/* Admin Routes */}
      <Route path="/admin/login" element={<Login />} />
      <Route path="/admin" element={<Navigate to="/admin/dashboard" replace />} />
      <Route path="/admin/dashboard" element={<AdminRoute element={<Dashboard />} />} />
      <Route path="/admin/applications" element={<AdminRoute element={<Applications />} />} />
      <Route path="/admin/contacts" element={<AdminRoute element={<Contacts />} />} />
      <Route path="/admin/careers" element={<AdminRoute element={<Careers />} />} />
      <Route path="/admin/properties" element={<AdminRoute element={<PropertiesAdmin />} />} />
      
      {/* Catch all unknown routes */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default AppRoutes;