import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

const Footer: React.FC = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  const footerAnimation = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5 }
  };

  return (
    <motion.footer 
      className="bg-white dark:bg-gray-900 text-gray-600 dark:text-gray-300 py-6 border-t border-gray-200 dark:border-gray-800"
      {...footerAnimation}
    >
      <div className="container-custom">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Company Info */}
          <div>
            <Link to="/" className="inline-block">
              <img src="/logo.png" alt="Darden PM" className="h-6" />
            </Link>
            <p className="text-gray-500 dark:text-gray-400 text-sm mt-2">
              {t('footer.description')}
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
              {t('footer.quickLinks')}
            </h4>
            <ul className="space-y-2 text-sm">
              {[
                { key: 'home', path: '/' },
                { key: 'properties', path: '/properties' },
                { key: 'services', path: '/services' },
                { key: 'about', path: '/about' },
                { key: 'contact', path: '/contact' }
              ].map(({ key, path }) => (
                <li key={key}>
                  <Link 
                    to={path}
                    className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green transition-colors"
                  >
                    {t(`navigation.${key}`)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
              {t('footer.legal')}
            </h4>
            <ul className="space-y-2 text-sm">
              {[
                { key: 'terms', path: '/terms' },
                { key: 'privacy', path: '/privacy' }
              ].map(({ key, path }) => (
                <li key={key}>
                  <Link 
                    to={path}
                    className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green transition-colors"
                  >
                    {t(`navigation.${key}`)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
              {t('contact.info.title')}
            </h4>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                <svg className="w-4 h-4 text-italian-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>{t('contact.info.email')}</span>
              </li>
              <li className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                <svg className="w-4 h-4 text-italian-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                <span>{t('contact.info.phone')}</span>
              </li>
              <li className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                <svg className="w-4 h-4 text-italian-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{t('contact.info.hours')}</span>
              </li>
            </ul>
          </div>

          {/* Social Media */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
              {t('footer.followUs')}
            </h4>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green transition-colors">
                <span className="sr-only">Facebook</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.77,7.46H14.5v-1.9c0-.9.6-1.1,1-1.1h3V.5h-4.33C10.24.5,9.5,3.44,9.5,5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4Z"/>
                </svg>
              </a>
              <a href="#" className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green transition-colors">
                <span className="sr-only">Twitter</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
              <a href="#" className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green transition-colors">
                <span className="sr-only">LinkedIn</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 dark:text-gray-400 text-xs">
              &copy; {currentYear} Darden Property & Management
            </p>
            <div className="flex space-x-4 mt-2 md:mt-0">
              <Link 
                to="/privacy" 
                className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green text-xs transition-colors"
              >
                {t('footer.privacy')}
              </Link>
              <Link 
                to="/terms" 
                className="text-gray-500 hover:text-italian-green dark:text-gray-400 dark:hover:text-italian-green text-xs transition-colors"
              >
                {t('footer.terms')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </motion.footer>
  );
};

export default Footer;
