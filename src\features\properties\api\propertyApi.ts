import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { Property } from '../propertiesSlice';

export const propertyApi = createApi({
  reducerPath: 'propertyApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  tagTypes: ['Property'],
  endpoints: (builder) => ({
    getProperties: builder.query<Property[], void>({
      query: () => 'properties',
      providesTags: ['Property'],
    }),
    getPropertyById: builder.query<Property, string>({
      query: (id) => `properties/${id}`,
      providesTags: (_result, _err, id) => [{ type: 'Property', id }],
    }),
    addProperty: builder.mutation<Property, Partial<Property>>({
      query: (body) => ({
        url: 'properties',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Property'],
    }),
  }),
});

export const {
  useGetPropertiesQuery,
  useGetPropertyByIdQuery,
  useAddPropertyMutation,
} = propertyApi;
