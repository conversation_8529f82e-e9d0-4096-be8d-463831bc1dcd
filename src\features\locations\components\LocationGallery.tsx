import React from 'react';
import { Box, Grid } from '@mui/material';
import { styled } from '@mui/material/styles';

interface ImageItem {
  url: string;
  alt: string;
}

interface LocationGalleryProps {
  images: ImageItem[];
}

const GalleryImage = styled('img')(({ theme }) => ({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  borderRadius: theme.shape.borderRadius,
  transition: 'transform 0.3s ease-in-out',
  '&:hover': {
    transform: 'scale(1.05)',
    cursor: 'pointer',
  },
}));

const ImageContainer = styled(Box)(({ theme }) => ({
  height: '250px',
  overflow: 'hidden',
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[2],
}));

const LocationGallery: React.FC<LocationGalleryProps> = ({ images }) => {

  return (
    <Box sx={{ width: '100%', my: 4 }}>
      <Grid container spacing={2}>
        {images.map((image, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <ImageContainer>
              <GalleryImage
                src={image.url}
                alt={image.alt}
                loading="lazy"
              />
            </ImageContainer>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default LocationGallery;