import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

interface LocationItem {
  id: string;
  name: string;
  description: string;
  images: {
    url: string;
    alt: string;
  }[];
}

const LocationHighlights: React.FC = () => {
  const { t } = useTranslation();
  const locations = t('locations.items', { returnObjects: true }) as LocationItem[];

  return (
    <section className="py-16 px-4 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-4xl font-bold text-center text-gray-900 dark:text-white mb-12">
          {t('locations.title')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {locations.map((location) => (
            <motion.div
              key={location.id}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
              className="h-full"
            >
              <Link
                to={`/locations/${location.id}`}
                className="block h-full no-underline"
              >
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden h-full flex flex-col">
                  <div
                    className="h-64 bg-cover bg-center"
                    style={{
                      backgroundImage: `url(${location.images[0].url})`,
                    }}
                    role="img"
                    aria-label={location.images[0].alt}
                  />
                  <div className="p-6 flex flex-col flex-grow">
                    <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-3">
                      {location.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4 flex-grow">
                      {location.description}
                    </p>
                    <div className="flex items-center text-italian-green hover:text-italian-green-dark transition-colors">
                      <span className="font-medium">
                        {t('locations.learnMore')}
                      </span>
                      <svg
                        className="w-5 h-5 ml-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M14 5l7 7m0 0l-7 7m7-7H3"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default LocationHighlights;
