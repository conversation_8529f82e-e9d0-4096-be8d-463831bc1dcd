import { supabase } from '@/core/supabase/client';

export interface CareerApplication {
  id?: string;
  career_id: string;
  career_title: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  self_presentation: string;
  motivation: string;
  resume_url: string;
  created_at?: string;
}

export const submitApplication = async (application: Omit<CareerApplication, 'id' | 'created_at'>) => {
  const { error } = await supabase
    .from('career_applications')
    .insert([application]);

  if (error) throw error;
  return true;
};

export const uploadResume = async (file: File, applicantEmail: string) => {
  const fileExt = file.name.split('.').pop();
  const fileName = `${applicantEmail}-${Date.now()}.${fileExt}`;
  const filePath = `resumes/${fileName}`;

  const { data, error } = await supabase.storage
    .from('applications')
    .upload(filePath, file);

  if (error) throw error;
  
  const { data: { publicUrl } } = supabase.storage
    .from('applications')
    .getPublicUrl(filePath);

  return publicUrl;
};
