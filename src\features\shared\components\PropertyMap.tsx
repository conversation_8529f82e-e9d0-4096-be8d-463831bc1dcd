import React, { useState } from 'react';
import { APIProvider, Map, AdvancedMarker } from '@vis.gl/react-google-maps';

interface PropertyMapProps {
  location: {
    city: string;
    area: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
}

const PropertyMap: React.FC<PropertyMapProps> = ({ location }) => {
  const [isLoaded, setIsLoaded] = useState(false);

  const mapStyles = {
    height: '400px',
    width: '100%',
    borderRadius: '0.75rem',
    filter: isLoaded ? 'none' : 'blur(5px)',
    transition: 'filter 0.3s ease-in-out'
  };

  const mapOptions = {
    mapId: '1d0d28574043877b',
    styles: [
      {
        featureType: "all",
        elementType: "labels.text.fill",
        stylers: [{ color: "#7c93a3" }]
      },
      {
        featureType: "administrative",
        elementType: "geometry.stroke",
        stylers: [{ color: "#dce1e4" }]
      },
      {
        featureType: "administrative.country",
        elementType: "geometry",
        stylers: [{ visibility: "on" }]
      },
      {
        featureType: "landscape",
        elementType: "geometry",
        stylers: [{ color: "#f5f5f5" }]
      },
      {
        featureType: "poi",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "poi.park",
        elementType: "geometry",
        stylers: [{ color: "#dcebd7" }]
      },
      {
        featureType: "road",
        elementType: "geometry",
        stylers: [{ color: "#ffffff" }]
      },
      {
        featureType: "road",
        elementType: "geometry.stroke",
        stylers: [{ color: "#e6e6e6" }]
      },
      {
        featureType: "transit",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "water",
        elementType: "geometry",
        stylers: [{ color: "#e9f1f4" }]
      }
    ],
    disableDefaultUI: true,
    zoomControl: true,
    scrollwheel: false,
    mapTypeControl: false,
    streetViewControl: false,
    rotateControl: false,
    fullscreenControl: false,
    gestureHandling: 'cooperative'
  };

  const containerStyle = {
    position: 'relative',
    width: '100%',
    height: '400px',
    borderRadius: '0.75rem',
    overflow: 'hidden'
  } as const;

  // Loading placeholder
  const loadingPlaceholder = (
    <div className="absolute inset-0 bg-gray-100 dark:bg-gray-800 animate-pulse">
      <div className="h-full w-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700"></div>
    </div>
  );

  return (
    <div style={containerStyle} className="shadow-lg">
      {!isLoaded && loadingPlaceholder}
      <APIProvider apiKey={import.meta.env.VITE_GOOGLE_MAPS_API_KEY}>
        <Map
          style={mapStyles}
          zoom={15}
          center={location.coordinates}
          mapId="1d0d28574043877b"
          defaultOptions={{
            ...mapOptions,
            mapId: undefined
          }}
          onLoad={() => setIsLoaded(true)}
        >
          <AdvancedMarker 
            position={location.coordinates}
            title={`${location.area}, ${location.city}`}
          >
            <div className="bg-white/90 px-2 py-1 rounded shadow-md backdrop-blur-sm text-[#008C45] font-bold text-sm">
              {location.area}
            </div>
          </AdvancedMarker>
        </Map>
      </APIProvider>
    </div>
  );
};

export default PropertyMap;
