import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { useApplications, JobApplication } from '../hooks/useApplications';

type SortField = 'created_at' | 'first_name' | 'last_name' | 'status';
type SortOrder = 'asc' | 'desc';

export const ApplicationsPage: React.FC = () => {
  const { t } = useTranslation();
  const { applications, loading, error, updateApplicationStatus } = useApplications();
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(order => (order === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const handleStatusChange = async (applicationId: string, newStatus: string) => {
    try {
      await updateApplicationStatus(applicationId, newStatus);
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  };

  const sortedAndFilteredApplications = React.useMemo(() => {
    let filtered = [...applications];
    
    if (statusFilter !== 'all') {
      filtered = filtered.filter(app => app.status === statusFilter);
    }

    return filtered.sort((a, b) => {
      if (sortField === 'created_at') {
        return sortOrder === 'asc' 
          ? new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          : new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
      
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      }
      return aValue < bValue ? 1 : -1;
    });
  }, [applications, statusFilter, sortField, sortOrder]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-italian-green"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/50 p-4 rounded-lg">
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300',
    accepted: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',
    rejected: 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300',
    review: 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300',
    archived: 'bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-300',
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            {t('admin.applications')}
          </h1>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-italian-green focus:border-italian-green"
          >
            <option value="all">{t('admin.allStatuses')}</option>
            <option value="pending">{t('admin.statusPending')}</option>
            <option value="accepted">{t('admin.statusAccepted')}</option>
            <option value="rejected">{t('admin.statusRejected')}</option>
            <option value="review">{t('admin.statusReview')}</option>
            <option value="archived">{t('admin.statusArchived')}</option>
          </select>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th
                  onClick={() => handleSort('created_at')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                >
                  {t('admin.date')}
                  {sortField === 'created_at' && (
                    <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                  )}
                </th>
                <th
                  onClick={() => handleSort('first_name')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                >
                  {t('admin.name')}
                  {sortField === 'first_name' && (
                    <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                  )}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.position')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.contact')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.resume')}
                </th>
                <th
                  onClick={() => handleSort('status')}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:text-gray-700 dark:hover:text-gray-300"
                >
                  {t('admin.status')}
                  {sortField === 'status' && (
                    <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                  )}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {sortedAndFilteredApplications.map((application) => (
                <tr key={application.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {format(new Date(application.created_at), 'PP')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {application.first_name} {application.last_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {application.career_title}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <div>{application.email}</div>
                    {application.phone && <div>{application.phone}</div>}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {application.resume_url && (
                      <a
                        href={application.resume_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-italian-green hover:text-italian-green/80"
                      >
                        {t('admin.viewResume')}
                      </a>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[application.status as keyof typeof statusColors]}`}>
                      {t(`admin.status${application.status.charAt(0).toUpperCase() + application.status.slice(1)}`)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <select
                      value={application.status}
                      onChange={(e) => handleStatusChange(application.id, e.target.value)}
                      className="rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-italian-green focus:border-italian-green text-sm"
                    >
                      <option value="pending">{t('admin.statusPending')}</option>
                      <option value="accepted">{t('admin.statusAccepted')}</option>
                      <option value="rejected">{t('admin.statusRejected')}</option>
                      <option value="review">{t('admin.statusReview')}</option>
                      <option value="archived">{t('admin.statusArchived')}</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
          )}
        </div>
      </div>

      {selectedApplication && (
        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden p-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {selectedApplication.first_name} {selectedApplication.last_name}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('admin.applications.fields.position')}: {selectedApplication.career_title}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('admin.applications.fields.email')}: {selectedApplication.email}
                {selectedApplication.phone && ` • ${selectedApplication.phone}`}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('admin.applications.fields.createdAt')}: {format(new Date(selectedApplication.created_at), 'PPP')}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[selectedApplication.status as keyof typeof statusColors]}`}>
                {t(`admin.common.status${selectedApplication.status.charAt(0).toUpperCase() + selectedApplication.status.slice(1)}`)}
              </span>
              <button
                onClick={() => setSelectedApplication(null)}
                className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
              >
                <span className="sr-only">{t('admin.common.close')}</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {selectedApplication.self_presentation && (
              <div>
                <h3 className="text-md font-medium text-gray-900 dark:text-white mb-2">
                  {t('admin.applications.fields.coverLetter')}
                </h3>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="whitespace-pre-wrap text-gray-600 dark:text-gray-300">
                    {selectedApplication.self_presentation}
                  </p>
                </div>
              </div>
            )}
            
            {selectedApplication.motivation && (
              <div>
                <h3 className="text-md font-medium text-gray-900 dark:text-white mb-2">
                  {t('admin.applications.fields.motivation')}
                </h3>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="whitespace-pre-wrap text-gray-600 dark:text-gray-300">
                    {selectedApplication.motivation}
                  </p>
                </div>
              </div>
            )}
          </div>
          
          {selectedApplication.resume_url && (
            <div className="mt-6">
              <a
                href={selectedApplication.resume_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-italian-green hover:bg-italian-green/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-italian-green"
              >
                <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                {t('admin.common.viewResume')}
              </a>
            </div>
          )}
        </div>
      )}
    </div>
  );
};