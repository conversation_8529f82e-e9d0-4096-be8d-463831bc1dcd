import { supabase } from '@/core/supabase/client';

export interface ContactMessage {
  id?: string;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  document_url?: string;
  created_at?: string;
  status?: 'new' | 'read' | 'replied' | 'archived';
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/jpeg',
  'image/png'
];

export const validateFile = (file: File): string | null => {
  if (file.size > MAX_FILE_SIZE) {
    return 'File size exceeds 5MB limit';
  }
  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return 'File type not supported. Please upload PDF, DOC, DOCX, JPG, or PNG files';
  }
  return null;
};

export const uploadDocument = async (file: File, email: string): Promise<string> => {
  // Validate file before upload
  const validationError = validateFile(file);
  if (validationError) {
    throw new Error(validationError);
  }

  const timestamp = new Date().getTime();
  const fileExt = file.name.split('.').pop()?.toLowerCase() || '';
  const cleanEmail = email.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
  const fileName = `${cleanEmail}-${timestamp}.${fileExt}`;
  const filePath = `contact-documents/${fileName}`;

  try {
    const { error: uploadError } = await supabase.storage
      .from('applications')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) throw uploadError;

    const { data: { publicUrl } } = supabase.storage
      .from('applications')
      .getPublicUrl(filePath);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading document:', error);
    throw new Error('Failed to upload document. Please try again.');
  }
};

export const submitContactMessage = async (message: Omit<ContactMessage, 'id' | 'created_at' | 'status'>) => {
  try {
    const { error } = await supabase
      .from('contact_messages')
      .insert([{
        ...message,
        status: 'new' // Set default status for new messages
      }]);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error submitting contact message:', error);
    throw new Error('Failed to submit message. Please try again.');
  }
};
