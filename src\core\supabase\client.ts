import { createClient } from '@supabase/supabase-js';

if (!import.meta.env.VITE_SUPABASE_URL) {
  throw new Error('Missing VITE_SUPABASE_URL');
}
if (!import.meta.env.VITE_SUPABASE_ANON_KEY) {
  throw new Error('Missing VITE_SUPABASE_ANON_KEY');
}

// Debug logging for Supabase configuration
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Mask the key for secure logging - only show first 4 and last 4 characters
const maskKey = (key: string) => {
  if (key.length <= 8) return '********';
  return `${key.substring(0, 4)}...${key.substring(key.length - 4)}`;
};

// Log connection details
console.debug('Initializing Supabase client with:');
console.debug(`URL: ${SUPABASE_URL}`);
console.debug(`Anon Key: ${maskKey(SUPABASE_ANON_KEY)}`);

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

console.debug('Supabase client initialized successfully');
