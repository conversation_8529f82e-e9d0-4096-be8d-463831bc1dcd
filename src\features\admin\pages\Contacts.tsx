import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { fr, enUS } from 'date-fns/locale';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { useContacts } from '@features/admin/hooks/useContacts';
import { companyInfo } from '@core/config/company';
import { 
  ExportFormat, 
  convertToCSV, 
  downloadFile, 
  validateContactData, 
  parseCSV,
  getFormattedDate 
} from '@features/admin/utils/exportImport';
import toast from '@features/admin/utils/toast';

// Import types for file handling
type FileType = 'csv' | 'excel' | 'pdf';

const ContactStatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const colors = {
    new: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    read: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    replied: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    archived: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[status]}`}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

const Contacts: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { 
    contacts, 
    loading, 
    error, 
    updateContactStatus, 
    refetchContacts 
  } = useContacts();
  const dateLocale = i18n.language === 'fr' ? fr : enUS;
  const [importError, setImportError] = useState<string | null>(null);

  // Export contacts
  const handleExport = async (type: FileType) => {
    try {
      const exportData = contacts.map(contact => ({
        name: contact.name,
        email: contact.email,
        phone: contact.phone || '',
        subject: contact.subject,
        message: contact.message,
        status: contact.status,
        created_at: format(new Date(contact.created_at), 'PPp', { locale: dateLocale })
      }));

      const fileName = `${companyInfo.name.toLowerCase().replace(/\s+/g, '-')}-contacts-${getFormattedDate()}`;

      switch (type) {
        case 'csv':
          // Add company info at the top of CSV
          const companyHeader = [
            ['Company:', companyInfo.name],
            ['Address:', `${companyInfo.address}, ${companyInfo.city}, ${companyInfo.province} ${companyInfo.postalCode}`],
            ['Contact:', `${companyInfo.phone} | ${companyInfo.email}`],
            ['Website:', companyInfo.website],
            [''], // Empty line for spacing
            ['Status Legend:'],
            ['new - Newly received messages'],
            ['read - Messages that have been viewed'],
            ['replied - Messages that have been responded to'],
            ['archived - Messages that have been archived'],
            [''], // Empty line for spacing
          ];
          
          const csvContent = companyHeader
            .map(row => row.join(','))
            .concat(['']) // Add empty line
            .concat(convertToCSV(exportData))
            .join('\n');
            
          downloadFile(csvContent, `${fileName}.csv`, 'text/csv;charset=utf-8;');
          break;

        case 'excel':
          const workbook = XLSX.utils.book_new();
          
          // Create company info worksheet
          const companySheet = XLSX.utils.aoa_to_sheet([
            [{ v: companyInfo.name, t: 's', s: { font: { bold: true, sz: 16 } } }],
            [''],
            ['Address:', `${companyInfo.address}`],
            ['', `${companyInfo.city}, ${companyInfo.province} ${companyInfo.postalCode}`],
            ['Phone:', companyInfo.phone],
            ['Email:', companyInfo.email],
            ['Website:', companyInfo.website],
            [''],
            ['Status Legend:'],
            ['new', 'Newly received messages'],
            ['read', 'Messages that have been viewed'],
            ['replied', 'Messages that have been responded to'],
            ['archived', 'Messages that have been archived']
          ]);

          // Style the contacts worksheet
          const worksheet = XLSX.utils.json_to_sheet(exportData);
          
          // Set column widths
          const colWidths = [
            { wch: 20 }, // name
            { wch: 30 }, // email
            { wch: 15 }, // phone
            { wch: 30 }, // subject
            { wch: 40 }, // message
            { wch: 15 }, // status
            { wch: 20 }  // created_at
          ];
          worksheet['!cols'] = colWidths;

          // Add both sheets to workbook
          XLSX.utils.book_append_sheet(workbook, companySheet, 'Company Info');
          XLSX.utils.book_append_sheet(workbook, worksheet, 'Contacts');
          
          XLSX.writeFile(workbook, `${fileName}.xlsx`);
          break;

        case 'pdf':
          const doc = new jsPDF();
          
          // Add company logo if available
          try {
            doc.addImage(companyInfo.logo, 'PNG', 15, 10, 30, 30);
          } catch (e) {
            console.warn('Could not add company logo:', e);
          }

          // Add company info
          doc.setFontSize(20);
          doc.setTextColor(44, 62, 80); // Dark blue
          doc.text(companyInfo.name, doc.internal.pageSize.width / 2, 20, { align: 'center' });
          
          doc.setFontSize(10);
          doc.setTextColor(100, 100, 100); // Gray
          doc.text([
            `${companyInfo.address}`,
            `${companyInfo.city}, ${companyInfo.province} ${companyInfo.postalCode}`,
            `${companyInfo.phone} | ${companyInfo.email}`,
            companyInfo.website
          ], doc.internal.pageSize.width / 2, 30, { align: 'center' });

          // Add status legend
          doc.setFontSize(12);
          doc.setTextColor(44, 62, 80);
          doc.text('Status Legend:', 15, 60);
          
          doc.setFontSize(10);
          doc.setTextColor(100, 100, 100);
          doc.text([
            'new - Newly received messages',
            'read - Messages that have been viewed',
            'replied - Messages that have been responded to',
            'archived - Messages that have been archived'
          ], 15, 70);

          // Add title
          doc.setFontSize(16);
          doc.setTextColor(44, 62, 80);
          doc.text('Contact Messages Report', doc.internal.pageSize.width / 2, 90, { align: 'center' });
          
          // Add date
          doc.setFontSize(10);
          doc.setTextColor(100, 100, 100);
          doc.text(`Generated on: ${format(new Date(), 'PPp', { locale: dateLocale })}`, 
            doc.internal.pageSize.width / 2, 97, { align: 'center' });

          // Add table
          (doc as any).autoTable({
            startY: 105,
            head: [['Name', 'Email', 'Subject', 'Status', 'Created At']],
            body: exportData.map(row => [row.name, row.email, row.subject, row.status, row.created_at]),
            headStyles: {
              fillColor: [44, 62, 80],
              textColor: 255,
              fontSize: 10,
              halign: 'center'
            },
            alternateRowStyles: {
              fillColor: [245, 245, 245]
            },
            bodyStyles: {
              fontSize: 9,
              textColor: [50, 50, 50]
            },
            margin: { top: 110 },
            didDrawPage: function(data: any) {
              // Add footer on each page
              const pageSize = doc.internal.pageSize;
              const pageHeight = pageSize.height ? pageSize.height : pageSize.getHeight();
              doc.setFontSize(8);
              doc.setTextColor(150, 150, 150);
              doc.text(
                `${companyInfo.name} - Page ${data.pageCount}`, 
                doc.internal.pageSize.width / 2, 
                pageHeight - 10, 
                { align: 'center' }
              );
            }
          });

          doc.save(`${fileName}.pdf`);
          break;
      }
    } catch (error) {
      console.error(`Error exporting as ${type}:`, error);
      toast.error(t('admin.contacts.export.error'));
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setImportError(null);

      // Parse the CSV file
      const importedData = await parseCSV(file);

      // Validate the data
      const validation = validateContactData(importedData);
      if (!validation.valid) {
        setImportError(validation.errors.join('\n'));
        return;
      }

      // Insert valid data into Supabase
      const { error } = await supabase
        .from('contact_messages')
        .insert(validation.validData.map(contact => ({
          name: contact.name,
          email: contact.email,
          phone: contact.phone,
          subject: contact.subject,
          message: contact.message,
          status: contact.status,
          created_at: contact.created_at,
          document_url: null
        })));

      if (error) {
        console.error('Supabase insert error:', error);
        setImportError(`Failed to import contacts: ${error.message}`);
        return;
      }

      // Clear the file input
      event.target.value = '';

      // Show success message
      toast.success(t('admin.contacts.import.success', { count: validation.validData.length }));

      // Refresh the contacts list
      await refetchContacts();

    } catch (error) {
      console.error('Error importing file:', error);
      setImportError(t('admin.contacts.import.error'));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-italian-green"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900 p-4 rounded-md">
        <p className="text-red-800 dark:text-red-200">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          {t('admin.contacts.title')}
        </h1>
        
        {/* Export/Import Controls */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <button
              className="bg-white dark:bg-gray-800 px-4 py-2 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-italian-green"
              onClick={() => document.getElementById('import-file')?.click()}
            >
              {t('admin.contacts.import.button')}
            </button>
            <input
              id="import-file"
              type="file"
              className="hidden"
              accept=".csv,.xlsx,.xls"
              onChange={handleImport}
            />
          </div>
          
          <div className="relative inline-block text-left">
            <button
              className="bg-italian-green text-white px-4 py-2 rounded-md shadow-sm text-sm font-medium hover:bg-italian-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-italian-green"
              onClick={() => document.getElementById('export-menu')?.classList.toggle('hidden')}
            >
              {t('admin.contacts.export.button')}
            </button>
            
            <div
              id="export-menu"
              className="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 dark:divide-gray-700"
            >
              <div className="py-1">
                <button
                  onClick={() => handleExport('csv')}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  {t('admin.contacts.export.csv')}
                </button>
                <button
                  onClick={() => handleExport('excel')}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  {t('admin.contacts.export.excel')}
                </button>
                <button
                  onClick={() => handleExport('pdf')}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  {t('admin.contacts.export.pdf')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {importError && (
        <div className="bg-red-50 dark:bg-red-900 p-4 rounded-md">
          <p className="text-red-800 dark:text-red-200">{importError}</p>
        </div>
      )}

      {contacts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">{t('admin.contacts.empty')}</p>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.contacts.table.name')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.contacts.table.email')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.contacts.table.subject')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.contacts.table.status')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.contacts.table.date')}
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('admin.contacts.table.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {contacts.map((contact) => (
                <tr key={contact.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">{contact.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 dark:text-gray-400">{contact.email}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 dark:text-white">{contact.subject}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 line-clamp-1">{contact.message}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <ContactStatusBadge status={contact.status} />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {format(new Date(contact.created_at), 'PPp', { locale: dateLocale })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      {contact.status === 'new' && (
                        <button
                          onClick={() => updateContactStatus(contact.id, 'read')}
                          className="text-italian-green hover:text-italian-green-dark dark:hover:text-italian-green-light"
                        >
                          {t('admin.contacts.markAsRead')}
                        </button>
                      )}
                      <button
                        onClick={() => updateContactStatus(contact.id, 'archived')}
                        className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
                      >
                        {t('admin.contacts.archive')}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default Contacts;
