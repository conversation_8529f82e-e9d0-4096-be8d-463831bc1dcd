import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Agent {
  id: string;
  name: string;
  title: string;
  image: string;
  phone: string;
  email: string;
}

export interface AgentState {
  currentAgent: Agent;
  loading: boolean;
  error: string | null;
}

const initialState: AgentState = {
  currentAgent: {
    id: 'agent1',
    name: '<PERSON><PERSON>',
    title: 'Senior Property Advisor',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    phone: '+1 (234) 567-890',
    email: '<EMAIL>'
  },
  loading: false,
  error: null
};

const agentSlice = createSlice({
  name: 'agent',
  initialState,
  reducers: {
    updateAgent: (state, action: PayloadAction<Partial<Agent>>) => {
      state.currentAgent = { ...state.currentAgent, ...action.payload };
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    }
  }
});

export const { updateAgent, setLoading, setError } = agentSlice.actions;
export default agentSlice.reducer;