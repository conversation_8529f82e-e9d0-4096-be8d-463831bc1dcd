import React from 'react';
import { useTranslation } from 'react-i18next';

const Properties: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          {t('admin.properties.title')}
        </h1>
      </div>
      
      {/* Placeholder content */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <p className="text-gray-500 dark:text-gray-400">
          {t('admin.properties.comingSoon')}
        </p>
      </div>
    </div>
  );
};

export default Properties;
