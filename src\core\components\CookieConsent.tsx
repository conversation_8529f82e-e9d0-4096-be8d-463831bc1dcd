import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useTranslation } from '../i18n/useTranslation';
import type { RootState } from '../../app/types';

interface CookieSettings {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

const defaultSettings: CookieSettings = {
  necessary: true,
  analytics: false,
  marketing: false,
  functional: false,
};

export const CookieConsent: React.FC = () => {
  const { t } = useTranslation();
  const { mode } = useSelector((state: RootState) => state.theme);
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [settings, setSettings] = useState<CookieSettings>(defaultSettings);

  useEffect(() => {
    const consent = localStorage.getItem('cookieConsent');
    if (!consent) {
      setIsVisible(true);
    } else {
      try {
        const savedSettings = JSON.parse(consent);
        if (typeof savedSettings === 'object' && savedSettings !== null) {
          setSettings({
            ...defaultSettings,
            ...savedSettings,
            necessary: true, // Always true
          });
          setIsVisible(false); // Hide banner if we have valid settings
        }
      } catch (error) {
        // If there's an error parsing the stored settings, reset to default
        localStorage.removeItem('cookieConsent');
        setIsVisible(true);
      }
    }
  }, []);

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
    };
    localStorage.setItem('cookieConsent', JSON.stringify(allAccepted));
    setSettings(allAccepted);
    setIsVisible(false);
    initializeCookies(allAccepted);
  };

  const handleSavePreferences = () => {
    const newSettings = { ...settings, necessary: true };
    localStorage.setItem('cookieConsent', JSON.stringify(newSettings));
    setIsVisible(false);
    initializeCookies(newSettings);
  };

  const handleDeclineAll = () => {
    const allDeclined = {
      ...defaultSettings,
      necessary: true, // Always true
    };
    localStorage.setItem('cookieConsent', JSON.stringify(allDeclined));
    setSettings(allDeclined);
    setIsVisible(false);
    initializeCookies(allDeclined);
  };

  const initializeCookies = (preferences: CookieSettings) => {
    // Clear existing data
    localStorage.removeItem('userBehavior');
    localStorage.removeItem('marketingPreferences');
    localStorage.removeItem('functionalPreferences');

    // Initialize cookies based on preferences
    if (preferences.analytics) {
      localStorage.setItem('userBehavior', JSON.stringify({
        lastVisit: new Date().toISOString(),
        pageViews: 0,
        interactions: {
          propertyViews: [],
          searches: [],
          favorites: []
        }
      }));
    }

    if (preferences.marketing) {
      localStorage.setItem('marketingPreferences', JSON.stringify({
        lastInteraction: new Date().toISOString(),
        interests: [],
        campaigns: []
      }));
    }

    if (preferences.functional) {
      localStorage.setItem('functionalPreferences', JSON.stringify({
        language: navigator.language,
        theme: mode,
        accessibility: {}
      }));
    }
  };

  if (!isVisible) return null;

  return (
    <div className={`fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 shadow-lg z-50 ${mode === 'dark' ? 'dark' : ''}`}>
      <div className="container-custom py-4 px-4">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div className="flex-1">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {t('cookies.message')}{' '}
                <Link to="/privacy" className="text-italian-green hover:underline">
                  {t('cookies.learnMore')}
                </Link>
              </p>
            </div>
            <div className="flex gap-4">
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="px-4 py-2 text-sm text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              >
                {t('cookies.preferences')}
              </button>
              <button
                onClick={handleDeclineAll}
                className="px-4 py-2 text-sm text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              >
                {t('cookies.declineAll')}
              </button>
              <button
                onClick={handleAcceptAll}
                className="px-4 py-2 text-sm text-white bg-italian-green hover:bg-italian-green-dark rounded-md transition-colors"
              >
                {t('cookies.acceptAll')}
              </button>
            </div>
          </div>

          {showDetails && (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">{t('cookies.necessary.title')}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('cookies.necessary.description')}</p>
                  </div>
                  <input type="checkbox" checked disabled className="h-4 w-4 text-italian-green" />
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">{t('cookies.analytics.title')}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('cookies.analytics.description')}</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.analytics}
                    onChange={(e) => setSettings({ ...settings, analytics: e.target.checked })}
                    className="h-4 w-4 text-italian-green cursor-pointer"
                  />
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">{t('cookies.marketing.title')}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('cookies.marketing.description')}</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.marketing}
                    onChange={(e) => setSettings({ ...settings, marketing: e.target.checked })}
                    className="h-4 w-4 text-italian-green cursor-pointer"
                  />
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">{t('cookies.functional.title')}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{t('cookies.functional.description')}</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.functional}
                    onChange={(e) => setSettings({ ...settings, functional: e.target.checked })}
                    className="h-4 w-4 text-italian-green cursor-pointer"
                  />
                </div>
              </div>
              <div className="flex justify-end mt-4">
                <button
                  onClick={handleSavePreferences}
                  className="px-4 py-2 text-sm text-white bg-italian-green hover:bg-italian-green-dark rounded-md transition-colors"
                >
                  {t('cookies.savePreferences')}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
