import { Contact } from '../hooks/useContacts';
import { MessageStatus } from '../hooks/useContacts';

export type ExportFormat = 'csv' | 'excel' | 'pdf';

// Function to convert contacts data to CSV string
export const convertToCSV = (contacts: Contact[]): string => {
  const headers = ['Name', 'Email', 'Subject', 'Message', 'Status', 'Created At'];
  const rows = contacts.map(contact => [
    contact.name,
    contact.email,
    contact.subject,
    contact.message,
    contact.status,
    new Date(contact.created_at).toLocaleString()
  ]);

  return [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n');
};

// Function to download file
export const downloadFile = (content: string, fileName: string, type: string) => {
  const blob = new Blob([content], { type });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

export interface ContactImportData {
  name: string;
  email: string;
  subject: string;
  message: string;
  phone?: string;
  status: MessageStatus;
  created_at?: string;
}

// Status mapping for legacy or different status values
const STATUS_MAPPING: Record<string, MessageStatus> = {
  'active': 'new',
  'on going': 'read',
  'completed': 'archived',
  'pending': 'new',
  'in progress': 'read',
  'done': 'archived',
  'new': 'new',
  'read': 'read',
  'replied': 'replied',
  'archived': 'archived'
};

const VALID_STATUSES: MessageStatus[] = ['new', 'read', 'replied', 'archived'];

// Function to validate imported data
export const validateContactData = (data: any[]): { valid: boolean; errors: string[]; validData: ContactImportData[] } => {
  const errors: string[] = [];
  const validData: ContactImportData[] = [];

  data.forEach((row, index) => {
    const rowNumber = index + 1;
    const rowErrors: string[] = [];

    // Required field validation
    if (!row.name || typeof row.name !== 'string' || row.name.trim() === '') {
      rowErrors.push(`Row ${rowNumber}: Missing or invalid name`);
    }
    if (!row.email || typeof row.email !== 'string' || !row.email.includes('@')) {
      rowErrors.push(`Row ${rowNumber}: Missing or invalid email`);
    }
    if (!row.subject || typeof row.subject !== 'string' || row.subject.trim() === '') {
      rowErrors.push(`Row ${rowNumber}: Missing or invalid subject`);
    }
    if (!row.message || typeof row.message !== 'string' || row.message.trim() === '') {
      rowErrors.push(`Row ${rowNumber}: Missing or invalid message`);
    }

    // Optional field validation
    if (row.phone && typeof row.phone !== 'string') {
      rowErrors.push(`Row ${rowNumber}: Invalid phone format`);
    }
    
    // Status validation and mapping
    const inputStatus = (row.status?.toLowerCase() || 'new').trim();
    const mappedStatus = STATUS_MAPPING[inputStatus];
    
    if (!mappedStatus) {
      rowErrors.push(`Row ${rowNumber}: Invalid status '${inputStatus}'. Will be converted to 'new'. Valid original statuses are: ${Object.keys(STATUS_MAPPING).join(', ')}`);
    }

    if (rowErrors.length > 0) {
      errors.push(...rowErrors);
    } else {
      validData.push({
        name: row.name.trim(),
        email: row.email.trim(),
        subject: row.subject.trim(),
        message: row.message.trim(),
        phone: row.phone?.trim() || null,
        status: mappedStatus || 'new',
        created_at: row.created_at || new Date().toISOString()
      });
    }
  });

  return {
    valid: errors.length === 0,
    errors,
    validData
  };
};

// Function to parse CSV data
export const parseCSV = async (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const csv = event.target?.result as string;
        const lines = csv.split('\n');
        const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
        const data = [];

        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line) {
            const values = line.split(',');
            const row: any = {};
            headers.forEach((header, index) => {
              row[header] = values[index]?.trim() || '';
            });
            data.push(row);
          }
        }

        resolve(data);
      } catch (error) {
        reject(new Error('Failed to parse CSV file. Please ensure it is properly formatted.'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
};

// Function to format date for file names
export const getFormattedDate = (): string => {
  const date = new Date();
  return date.toISOString().split('T')[0];
};
