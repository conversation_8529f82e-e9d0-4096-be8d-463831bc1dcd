import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../supabase/client';
import { PostgrestError } from '@supabase/supabase-js';

interface QueryState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  errorDetails?: any;
  lastFetched: number | null;
  retryCount: number;
}

interface QueryOptions {
  table: string;
  select?: string;
  orderBy?: { column: string; ascending?: boolean };
  filter?: { column: string; operator: string; value: any }[];
  limit?: number;
  retryLimit?: number;
}

const DEFAULT_RETRY_LIMIT = 3;
const RETRY_DELAY = 1500;

export function useSupabaseQuery<T>(options: QueryOptions) {
  const optionsRef = useRef({...options, retryLimit: options.retryLimit || DEFAULT_RETRY_LIMIT});
  const [state, setState] = useState<QueryState<T>>({
    data: [],
    loading: true,
    error: null,
    lastFetched: null,
    retryCount: 0
  });

  const fetchData = useCallback(async (retryAttempt = 0) => {
    console.log(`[useSupabaseQuery] Fetching data from ${optionsRef.current.table}, attempt ${retryAttempt + 1}`);
    setState(prev => ({ ...prev, loading: true, retryCount: retryAttempt }));
    
    try {
      // Build the query
      let query = supabase
        .from(optionsRef.current.table)
        .select(optionsRef.current.select || '*');

      if (optionsRef.current.filter) {
        optionsRef.current.filter.forEach(({ column, operator, value }) => {
          query = query.filter(column, operator, value);
        });
      }

      if (optionsRef.current.orderBy) {
        query = query.order(optionsRef.current.orderBy.column, {
          ascending: optionsRef.current.orderBy.ascending ?? false
        });
      }

      if (optionsRef.current.limit) {
        query = query.limit(optionsRef.current.limit);
      }

      // Execute the query
      console.log(`[useSupabaseQuery] Executing query for ${optionsRef.current.table}`);
      const { data, error, status } = await query;
      
      // Handle errors
      if (error) {
        console.error(`[useSupabaseQuery] Error fetching data from ${optionsRef.current.table}:`, error);
        throw error;
      }
      
      // Validate data
      if (!data) {
        console.warn(`[useSupabaseQuery] No data received from ${optionsRef.current.table}`);
        throw new Error('No data received');
      }

      console.log(`[useSupabaseQuery] Successfully fetched ${data.length} records from ${optionsRef.current.table}`);
      
      // Update state with fetched data
      setState({
        data: data as T[],
        loading: false,
        error: null,
        lastFetched: Date.now(),
        retryCount: 0
      });
    } catch (err) {
      console.error(`[useSupabaseQuery] Error in fetchData for ${optionsRef.current.table}:`, err);
      
      let errorMessage = `Error fetching ${optionsRef.current.table} data`;
      let errorDetails = null;
      
      if (err instanceof PostgrestError) {
        errorMessage = err.message;
        errorDetails = {
          code: err.code,
          details: err.details,
          hint: err.hint,
          message: err.message
        };
      } else if (err instanceof Error) {
        errorMessage = err.message;
        errorDetails = {
          name: err.name,
          stack: err.stack,
          timestamp: new Date().toISOString()
        };
      }

      // Implement retry logic
      if (retryAttempt < optionsRef.current.retryLimit) {
        console.warn(`[useSupabaseQuery] Retrying fetch for ${optionsRef.current.table} in ${RETRY_DELAY}ms (attempt ${retryAttempt + 1}/${optionsRef.current.retryLimit})`);
        setTimeout(() => fetchData(retryAttempt + 1), RETRY_DELAY);
      } else {
        console.error(`[useSupabaseQuery] Max retry attempts reached for ${optionsRef.current.table}`);
        setState(prev => ({
          ...prev,
          loading: false,
          error: errorMessage,
          errorDetails,
          retryCount: retryAttempt
        }));
      }
    }
  }, []); // Empty dependency array since we use ref

  const updateRecord = useCallback(async (id: string, updates: Partial<T>) => {
    console.log(`[useSupabaseQuery] Updating record ${id} in ${optionsRef.current.table}`, updates);
    try {
      const { data, error } = await supabase
        .from(optionsRef.current.table)
        .update(updates)
        .eq('id', id)
        .select();

      if (error) {
        console.error(`[useSupabaseQuery] Error updating record in ${optionsRef.current.table}:`, error);
        throw error;
      }

      console.log(`[useSupabaseQuery] Successfully updated record ${id} in ${optionsRef.current.table}`);
      
      // Update local state to reflect the change
      setState(prev => ({
        ...prev,
        data: prev.data.map(item => 
          (item as any).id === id ? { ...item, ...updates } : item
        )
      }));

      return data;
    } catch (err) {
      console.error(`[useSupabaseQuery] Failed to update record ${id} in ${optionsRef.current.table}:`, err);
      throw err;
    }
  }, []); // Empty dependency array since we use ref

  // Update ref when options change
  useEffect(() => {
    optionsRef.current = {...options, retryLimit: options.retryLimit || DEFAULT_RETRY_LIMIT};
  }, [options]);

  // Initial fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refetch: () => fetchData(0),
    updateRecord
  };
}