import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from '@/core/i18n/useTranslation';
import TypewriterText from '../../contact/components/TypewriterText';
import { useSelector } from 'react-redux';
import type { RootState } from '@/app/store';

const Career: React.FC = () => {
  const { t } = useTranslation();
  const { currentLanguage } = useSelector((state: RootState) => state.language);
  const positions = t('career.positions', { returnObjects: true, lng: currentLanguage }) || [];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen py-16">
      <div className="container mx-auto px-4">
        {/* Decorative Elements */}
        <div className="absolute top-40 left-10 w-24 h-24 bg-italian-green/20 rounded-full blur-3xl -z-10"></div>
        <div className="absolute top-80 right-20 w-32 h-32 bg-italian-red/20 rounded-full blur-3xl -z-10"></div>
        
        {/* Header */}
        <div className="relative mb-16">
          <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-italian-green/10 text-italian-green text-xs font-semibold px-3 py-1 rounded-full">
            {t('navigation.career')}
          </span>
          
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              <TypewriterText text={t('career.title')} delay={70} />
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              {t('career.subtitle')}
            </p>
          </motion.div>
        </div>

        {/* Job Positions */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12"
        >
          {positions.map((position: any, index: number) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
            >
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {position.title}
              </h3>
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                <span className="mr-4">{position.type}</span>
                <span>{position.duration}</span>
              </div>
              
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  {t('career.responsibilities')}
                </h4>
                <ul className="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                  {position.tasks.map((task: string, taskIndex: number) => (
                    <li key={taskIndex}>{task}</li>
                  ))}
                </ul>
              </div>
              
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  {t('career.requirements')}
                </h4>
                <ul className="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                  {position.requirements.map((req: string, reqIndex: number) => (
                    <li key={reqIndex}>{req}</li>
                  ))}
                </ul>
              </div>
              
              <Link
                to={`/career/apply/position-${index}`}
                state={{ 
                  careerId: `position-${index}`,
                  careerTitle: position.title
                }}
                className="block w-full bg-italian-green text-white py-2 px-4 rounded-md hover:bg-italian-green/90 transition-colors text-center"
              >
                {t('career.apply')}
              </Link>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default Career;