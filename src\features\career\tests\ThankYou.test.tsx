import React from 'react';
import { render, screen } from '@testing-library/react';
import ThankYou from '../components/ThankYou';

// Mock the translations
jest.mock('@/core/i18n/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe('ThankYou', () => {
  it('renders thank you message', () => {
    render(<ThankYou />);
    
    expect(screen.getByText(/career.application.thankYou/)).toBeInTheDocument();
    expect(screen.getByText(/career.application.success/)).toBeInTheDocument();
  });

  it('renders back to career link', () => {
    render(<ThankYou />);
    
    const link = screen.getByText(/common.backToCareer/);
    expect(link).toBeInTheDocument();
    expect(link.closest('a')).toHaveAttribute('href', '/career');
  });
});
