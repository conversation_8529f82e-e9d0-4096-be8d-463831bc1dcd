import React from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { t } = useTranslation();
  const location = useLocation();

  // Generate breadcrumbs from current location
  const breadcrumbs = location.pathname
    .split('/')
    .filter(Boolean)
    .map((path, index, array) => ({
      name: t(`admin.breadcrumbs.${path}`),
      href: '/' + array.slice(0, index + 1).join('/'),
      current: index === array.length - 1,
    }));

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <Sidebar />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <Header />

          {/* Main Content Area */}
          <main className="flex-1 overflow-y-auto">
            {/* Breadcrumbs */}
            <nav className="bg-white dark:bg-gray-800 shadow-sm" aria-label="Breadcrumb">
              <div className="px-4 sm:px-6 lg:px-8">
                <div className="flex items-center h-10">
                  <ol className="flex items-center space-x-4">
                    {breadcrumbs.map((breadcrumb, index) => (
                      <li key={breadcrumb.href}>
                        <div className="flex items-center">
                          {index !== 0 && (
                            <svg
                              className="flex-shrink-0 h-5 w-5 text-gray-400"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                              aria-hidden="true"
                            >
                              <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                          )}
                          <a
                            href={breadcrumb.href}
                            className={`${
                              index !== 0 ? 'ml-4' : ''
                            } text-sm font-medium ${
                              breadcrumb.current
                                ? 'text-italian-green'
                                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                            }`}
                            aria-current={breadcrumb.current ? 'page' : undefined}
                          >
                            {breadcrumb.name}
                          </a>
                        </div>
                      </li>
                    ))}
                  </ol>
                </div>
              </div>
            </nav>

            {/* Page Content */}
            <div className="py-6">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  {children}
                </motion.div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
