export const admin = {
  breadcrumbs: {
    contact: 'Contact',
    admin: 'Admin',
    contacts: 'Messages',
    applications: 'Candidatures',
    dashboard: 'Tableau de Bord',
  },
  nav: {
    dashboard: 'Tableau de Bord',
    applications: 'Candidatures',
    contacts: 'Messages',
    careers: 'Carrières',
    properties: 'Propriétés',
  },
  auth: {
    login: {
      title: 'Connectez-vous à votre compte',
      email: 'Adresse e-mail',
      password: 'Mot de passe',
      rememberMe: 'Se souvenir de moi',
      submit: 'Se connecter',
      error: 'Email ou mot de passe invalide',
    },
  },
  dashboard: {
    title: 'Tableau de Bord',
    totalProperties: 'Total des Propriétés',
    newApplications: 'Nouvelles Candidatures',
    unreadMessages: 'Messages Non Lus',
    activePositions: 'Postes Actifs',
    recentActivity: 'Activité Récente',
    activityPlaceholder: 'Aucune activité récente',
  },
  // Enhanced applications translations
  applications: {
    title: 'Candidatures',
    empty: 'Aucune candidature trouvée',
    table: {
      name: 'Nom',
      email: 'Email',
      position: 'Poste',
      date: 'Date',
      actions: 'Actions',
    },
    actions: {
      viewResume: 'Voir CV',
      download: 'Télécharger les fichiers',
      changeStatus: 'Changer de statut',
      delete: 'Supprimer'
    },
    status: {
      pending: 'En attente',
      review: 'En cours d\'examen',
      accepted: 'Acceptée',
      rejected: 'Rejetée',
      archived: 'Archivée',
      updated: 'Statut mis à jour avec succès'
    },
    errors: {
      notFound: 'Candidature non trouvée',
      invalidTransition: 'Transition de statut invalide',
      updateFailed: 'Échec de la mise à jour du statut de la candidature'
    },
    fields: {
      id: 'ID',
      name: 'Nom',
      email: 'Email',
      phone: 'Téléphone',
      position: 'Poste',
      status: 'Statut',
      cv: 'CV',
      coverLetter: 'Lettre de motivation',
      createdAt: 'Candidature le',
      updatedAt: 'Dernière mise à jour'
    },
    list: {
      title: 'Candidatures d\'emploi',
      empty: 'Aucune candidature trouvée',
      search: 'Rechercher des candidatures'
    }
  },
  // Enhanced contacts translations
  contacts: {
    title: 'Messages de Contact',
    empty: 'Aucun message trouvé',
    table: {
      name: 'Nom',
      email: 'Email',
      subject: 'Sujet',
      message: 'Message',
      date: 'Date',
      actions: 'Actions',
    },
    actions: {
      view: 'Voir',
      delete: 'Supprimer',
      archive: 'Archiver',
      reply: 'Répondre'
    },
    status: {
      new: 'Nouveau',
      read: 'Lu',
      replied: 'Répondu',
      archived: 'Archivé',
      updated: 'Statut mis à jour avec succès',
      error: 'Échec de la mise à jour du statut'
    },
    errors: {
      notFound: 'Message non trouvé',
      invalidTransition: 'Transition de statut invalide'
    },
    fields: {
      id: 'ID',
      name: 'Nom',
      email: 'Email',
      phone: 'Téléphone',
      subject: 'Sujet',
      message: 'Message',
      attachment: 'Pièce jointe',
      createdAt: 'Reçu le',
      status: 'Statut'
    }
  },
  // Common admin translations
  common: {
    loading: 'Chargement...',
    error: 'Une erreur est survenue',
    retry: 'Réessayer',
    save: 'Enregistrer',
    cancel: 'Annuler',
    delete: 'Supprimer',
    edit: 'Modifier',
    view: 'Voir',
    search: 'Rechercher',
    filter: 'Filtrer',
    sort: 'Trier',
    actions: 'Actions',
    status: 'Statut',
    date: 'Date',
    name: 'Nom',
    email: 'Email',
    phone: 'Téléphone',
    subject: 'Sujet',
    message: 'Message',
    position: 'Poste',
    from: 'De',
    close: 'Fermer',
    viewAttachment: 'Voir la pièce jointe',
    viewResume: 'Voir le CV',
    allStatuses: 'Tous les statuts',
    statusNew: 'Nouveau',
    statusRead: 'Lu',
    statusReplied: 'Répondu',
    statusArchived: 'Archivé',
    statusPending: 'En attente',
    statusAccepted: 'Accepté',
    statusRejected: 'Rejeté',
    statusReview: 'En cours d\'examen'
  }
} as const;