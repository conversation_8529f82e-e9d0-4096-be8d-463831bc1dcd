@tailwind base;
@tailwind components;
@tailwind utilities;

/* Text shadow effect for better readability over images */
.shadow-text {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

:root {
  --color-primary: #008C45;
  --color-secondary: #CD212A;
  --color-neutral: #F1F5F9;
  --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  --gradient-secondary: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-light));
  --gradient-accent: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
}

/* Dark mode custom properties */
.dark {
  --color-dark-bg: #1E293B;
  --color-dark-surface: #334155;
  --color-dark-text: #F1F5F9;
  --color-dark-border: rgba(255, 255, 255, 0.1);
  --color-dark-shadow: rgba(0, 0, 0, 0.3);
}

@layer base {
  .dark {
    color-scheme: dark;
  }
  
  /* Add custom scrollbar for dark mode */
  .dark ::-webkit-scrollbar {
    width: 10px;
  }
  
  .dark ::-webkit-scrollbar-track {
    background: var(--color-dark-surface);
  }
  
  .dark ::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border-radius: 5px;
  }
  
  .dark ::-webkit-scrollbar-thumb:hover {
    background: #00B358;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  }

  .btn-primary {
    @apply bg-italian-green text-white hover:bg-italian-green-light;
  }

  .btn-secondary {
    @apply bg-italian-red text-white hover:bg-italian-red-light;
  }

  /* Container Component */
  .container-custom {
    @apply container mx-auto px-4;
  }

  /* Gradient Text Component */
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-accent;
  }

  /* Hover Effect Components */
  .gradient-hover {
    @apply relative overflow-hidden;
  }

  .gradient-hover::after {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-300 bg-gradient-primary;
  }

  .gradient-hover:hover::after {
    @apply opacity-10;
  }
  
  /* Card components with dark mode support */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg transition-all duration-300;
  }
  
  /* Dark mode text enhancements */
  .dark .heading {
    @apply text-gray-100;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  .dark .paragraph {
    @apply text-gray-300;
  }
  
  .dark .textured-bg {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }
  
  .bg-grid-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M0 0h40v40H0V0zm1 1h38v38H1V1z'/%3E%3C/g%3E%3C/svg%3E");
    background-size: 30px 30px;
  }
  
  .dark .bg-grid-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M0 0h40v40H0V0zm1 1h38v38H1V1z'/%3E%3C/g%3E%3C/svg%3E");
  }
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Add advanced layering effects for dark mode */
.dark .layered-card {
  position: relative;
  background-color: rgba(51, 65, 85, 0.7);
  border: 1px solid var(--color-dark-border);
  box-shadow: 0 8px 24px var(--color-dark-shadow);
  backdrop-filter: blur(12px);
}

/* Add subtle texture to dark backgrounds */