import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import TypewriterText from './TypewriterText';
import { uploadDocument, submitContactMessage } from '../api/contact';

const Contact: React.FC = () => {
  const { t } = useTranslation();
  const [formState, setFormState] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [showChatbot, setShowChatbot] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formState.name || !formState.email || !formState.message) {
      setStatus('error');
      return;
    }

    try {
      let documentUrl = '';
      if (uploadedFile) {
        documentUrl = await uploadDocument(uploadedFile, formState.email);
      }

      await submitContactMessage({
        ...formState,
        document_url: documentUrl
      });

      setStatus('success');
      setFormState({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
      });
      setUploadedFile(null);
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setStatus('error');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormState(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, file: t('contact.fileSizeError') }));
        e.target.value = '';
        return;
      }
      // Check file type
      const validTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];
      if (!validTypes.some(type => file.type.includes(type) || file.name.endsWith(type))) {
        setErrors(prev => ({ ...prev, file: t('contact.fileTypeError') }));
        e.target.value = '';
        return;
      }
      setUploadedFile(file);
      setErrors(prev => ({ ...prev, file: undefined }));
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen py-16">
      <div className="container mx-auto px-4">
        {/* Decorative Elements */}
        <div className="absolute top-40 left-10 w-24 h-24 bg-italian-green/20 rounded-full blur-3xl -z-10"></div>
        <div className="absolute top-80 right-20 w-32 h-32 bg-italian-red/20 rounded-full blur-3xl -z-10"></div>
        
        {/* Header */}
        <div className="relative">
          <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-italian-green/10 text-italian-green text-xs font-semibold px-3 py-1 rounded-full">
            {t('navigation.contact')}
          </span>
        </div>
        
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            <TypewriterText text={t('contact.title')} delay={70} />
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            <TypewriterText text={t('contact.subtitle')} delay={50} />
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 relative overflow-hidden group"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="absolute top-0 left-0 w-20 h-2 bg-italian-green"></div>
            <div className="absolute top-0 right-0 w-20 h-2 bg-italian-red"></div>
            <div className="absolute -top-20 -right-20 w-40 h-40 bg-italian-green/5 rounded-full group-hover:scale-150 transition-all duration-700"></div>
            
            <form onSubmit={handleSubmit} className="space-y-6 relative">
              {[
                { name: 'name', type: 'text', label: t('contact.form.name') },
                { name: 'email', type: 'email', label: t('contact.form.email') },
                { name: 'phone', type: 'tel', label: t('contact.form.phone') },
                { name: 'subject', type: 'text', label: t('contact.form.subject') }
              ].map((field) => (
                <motion.div key={field.name} variants={itemVariants}>
                  <label 
                    htmlFor={field.name}
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                  >
                    {field.label}
                  </label>
                  <input
                    type={field.type}
                    id={field.name}
                    name={field.name}
                    value={formState[field.name as keyof typeof formState]}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:text-white transition-all duration-300"
                    required={field.name !== 'phone'}
                  />
                </motion.div>
              ))}

              <motion.div variants={itemVariants}>
                <label 
                  htmlFor="document"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  {t('contact.form.document')}
                </label>
                <input
                  type="file"
                  id="document"
                  name="document"
                  onChange={handleFileChange}
                  className="w-full px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:text-white transition-all duration-300"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                />
                <div className="mt-1 space-y-1">
                  <p className="text-xs text-gray-500">{t('contact.form.documentHelp')}</p>
                  <p className="text-xs text-gray-500">{t('contact.form.upload.formats')}</p>
                  <p className="text-xs text-gray-500">{t('contact.form.upload.size')}</p>
                </div>
                {errors.file && (
                  <p className="text-sm text-red-600 mt-1">{errors.file}</p>
                )}
              </motion.div>

              <motion.div variants={itemVariants}>
                <label 
                  htmlFor="message"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  {t('contact.form.message')}
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formState.message}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-italian-green focus:border-italian-green dark:text-white transition-all duration-300"
                  required
                />
              </motion.div>

              <motion.div 
                className="flex justify-between items-center"
                variants={itemVariants}
              >
                <label className="flex items-center space-x-2 text-sm">
                  <input type="checkbox" className="form-checkbox text-italian-green rounded border-gray-300 focus:ring-italian-green" />
                  <span className="text-gray-700 dark:text-gray-300">I agree to the Privacy Policy</span>
                </label>
              </motion.div>

              <motion.button
                type="submit"
                className="w-full py-3 px-6 bg-gradient-to-r from-italian-green to-italian-green-dark text-white font-bold rounded-md shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1"
                variants={itemVariants}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {t('contact.form.submit')}
              </motion.button>

              {status === 'success' && (
                <motion.div
                  className="mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {t('contact.form.success')}
                </motion.div>
              )}

              {status === 'error' && (
                <motion.div
                  className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {t('contact.form.error')}
                </motion.div>
              )}
            </form>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="flex flex-col h-full"
          >
            {/* Contact Info Card */}
            <motion.div 
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 mb-8 relative overflow-hidden group"
              variants={itemVariants}
            >
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-italian-green to-italian-red"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-italian-red/5 rounded-full group-hover:scale-150 transition-all duration-700"></div>
              
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 relative">
                {t('contact.info.title')}
              </h3>
              
              <ul className="space-y-6 relative">
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-italian-green/10 rounded-full flex items-center justify-center mr-4">
                    <svg className="w-5 h-5 text-italian-green" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-bold text-gray-700 dark:text-gray-300 mb-1">Address</h4>
                    <p className="text-gray-600 dark:text-gray-400">{t('contact.info.address')}</p>
                  </div>
                </li>
                
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-italian-red/10 rounded-full flex items-center justify-center mr-4">
                    <svg className="w-5 h-5 text-italian-red" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-bold text-gray-700 dark:text-gray-300 mb-1">Email</h4>
                    <p className="text-gray-600 dark:text-gray-400">{t('contact.info.email')}</p>
                  </div>
                </li>
                
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-italian-green/10 rounded-full flex items-center justify-center mr-4">
                    <svg className="w-5 h-5 text-italian-green" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-bold text-gray-700 dark:text-gray-300 mb-1">Phone</h4>
                    <p className="text-gray-600 dark:text-gray-400">{t('contact.info.phone')}</p>
                  </div>
                </li>
                
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-italian-red/10 rounded-full flex items-center justify-center mr-4">
                    <svg className="w-5 h-5 text-italian-red" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-bold text-gray-700 dark:text-gray-300 mb-1">Business Hours</h4>
                    <p className="text-gray-600 dark:text-gray-400">{t('contact.info.hours')}</p>
                  </div>
                </li>
              </ul>
            </motion.div>
            
            {/* Map */}
            <motion.div 
              className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 flex-grow relative overflow-hidden"
              variants={itemVariants}
            >
              <div className="absolute top-0 left-0 w-full h-2 bg-italian-red"></div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Our Location</h3>
              <div className="w-full h-64 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  <p className="text-gray-500 dark:text-gray-400">Map loading...</p>
                </div>
                {/* Placeholder for actual map - Would be replaced with a real map component */}
                <iframe 
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3323.686903032749!2d-7.6294814!3d33.5950627!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xda7d2f03266f69d%3A0x3b62e51bc1ea4cbe!2sCasablanca%20Finance%20City%20Tower!5e0!3m2!1sen!2sus!4v1644334962301!5m2!1sen!2sus" 
                  className="w-full h-full border-0" 
                  allowFullScreen 
                  loading="lazy"
                  title="Office Location Map"
                ></iframe>
              </div>
            </motion.div>
          </motion.div>
        </div>
        
        {/* Chat Bot Toggle Button */}
        <motion.button
          className="fixed bottom-6 right-6 w-14 h-14 rounded-full bg-italian-green text-white shadow-lg hover:shadow-xl flex items-center justify-center z-50"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setShowChatbot(!showChatbot)}
        >
          {showChatbot ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          )}
        </motion.button>

        {/* Chatbot */}
        {showChatbot && (
          <motion.div
            className="fixed bottom-24 right-6 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-2xl z-50 overflow-hidden"
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.8 }}
          >
            <div className="bg-italian-green p-4 text-white">
              <h3 className="font-bold">Chat with Us</h3>
              <p className="text-xs">We typically reply within a few minutes</p>
            </div>
            <div className="h-80 p-4 overflow-y-auto">
              <div className="mb-4">
                <div className="bg-italian-green/10 text-italian-green rounded-lg p-3 inline-block">
                  Hello! How can I help you today?
                </div>
              </div>
              {/* Chat messages would go here */}
            </div>
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex">
                <input
                  type="text"
                  className="flex-grow bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-l-md px-4 py-2 focus:ring-italian-green focus:border-italian-green dark:text-white"
                  placeholder="Type your message..."
                />
                <button
                  className="bg-italian-green text-white px-4 py-2 rounded-r-md"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Contact;