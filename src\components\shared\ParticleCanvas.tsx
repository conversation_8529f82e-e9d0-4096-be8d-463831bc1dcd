import React, { useEffect, useRef, useState } from 'react';
import { particleUtils, particleConfig } from '../../utils/animations';

interface ParticleCanvasProps {
  opacity?: number;
  zIndex?: number;
}

const ParticleCanvas: React.FC<ParticleCanvasProps> = ({
  opacity = 0.1,
  zIndex = -1
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [dimensions, setDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });
  const animationFrameRef = useRef<number>();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Initialize the particle system
    const particles = Array.from({ length: particleConfig.count }, () => 
      particleUtils.createParticle(canvas)
    );

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, dimensions.width, dimensions.height);
      
      // Update and draw each particle
      particles.forEach(particle => {
        particleUtils.updateParticle(particle, canvas);
        particleUtils.drawParticle(ctx, particle);
      });

      // Draw connections between particles
      particleUtils.drawConnections(ctx, particles);
      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animate();

    // Handle window resize
    const handleResize = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [dimensions]);

  // Update canvas size when dimensions change
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.width = dimensions.width;
    canvas.height = dimensions.height;
  }, [dimensions]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        opacity,
        zIndex,
        pointerEvents: 'none'
      }}
    />
  );
};

export default ParticleCanvas;
