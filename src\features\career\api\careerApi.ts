import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

interface JobApplication {
  jobId: string;
  name: string;
  email: string;
  resume: File;
  coverLetter?: string;
}

export const careerApi = createApi({
  reducerPath: 'careerApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  tagTypes: ['Job'],
  endpoints: (builder) => ({
    getJobs: builder.query({
      query: () => 'jobs',
      providesTags: ['Job'],
    }),
    applyForJob: builder.mutation<void, JobApplication>({
      query: (application) => ({
        url: 'jobs/apply',
        method: 'POST',
        body: application,
      }),
    }),
  }),
});

export const {
  useGetJobsQuery,
  useApplyForJobMutation,
} = careerApi;
