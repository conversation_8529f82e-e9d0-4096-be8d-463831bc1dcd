import React, { useEffect, useState, useRef } from 'react';
import { motion, Variants } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  color: string;
  alpha: number;
}

const HeroSection: React.FC = () => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [particles, setParticles] = useState<Particle[]>([]);

  // Initialize particles
  useEffect(() => {
    const initParticles = () => {
      const newParticles: Particle[] = [];
      const numParticles = 50;
      const colors = ['#008C45', '#00B358', '#006633'];

      for (let i = 0; i < numParticles; i++) {
        newParticles.push({
          x: Math.random() * window.innerWidth,
          y: Math.random() * window.innerHeight,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          size: Math.random() * 3 + 1,
          color: colors[Math.floor(Math.random() * colors.length)],
          alpha: Math.random() * 0.5 + 0.1
        });
      }
      setParticles(newParticles);
    };

    initParticles();
    window.addEventListener('resize', initParticles);
    return () => window.removeEventListener('resize', initParticles);
  }, []);

  // Animate particles
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const animate = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      particles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.alpha;
        ctx.fill();
      });

      requestAnimationFrame(animate);
    };

    animate();
  }, [particles]);

  // Creative reveal animation for title
  const titleVariants: Variants = {
    hidden: { 
      opacity: 0,
      clipPath: "polygon(0 0, 0 0, 0 100%, 0% 100%)"
    },
    visible: {
      opacity: 1,
      clipPath: "polygon(0 0, 100% 0, 100% 100%, 0 100%)",
      transition: {
        duration: 1.2,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  // Subtle fade-in for description
  const descriptionVariants: Variants = {
    hidden: { 
      opacity: 0,
      y: 10
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        delay: 0.3,
        ease: "easeOut"
      }
    }
  };

  // Button hover animation
  const buttonVariants: Variants = {
    initial: { scale: 1 },
    hover: { 
      scale: 1.05,
      boxShadow: "0 10px 30px rgba(0, 140, 69, 0.2)",
      transition: {
        duration: 0.2,
        ease: "easeInOut"
      }
    },
    tap: { 
      scale: 0.98,
      boxShadow: "0 5px 15px rgba(0, 140, 69, 0.1)"
    }
  };

  return (
    <section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden">
      {/* Dynamic background with patterns */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute inset-0"
          style={{
            background: `
              radial-gradient(circle at 0% 0%, rgba(0,140,69,0.15) 0%, transparent 50%),
              linear-gradient(45deg, rgba(0,140,69,0.05) 25%, transparent 25%, transparent 75%, rgba(0,140,69,0.05) 75%),
              linear-gradient(-45deg, rgba(0,140,69,0.05) 25%, transparent 25%, transparent 75%, rgba(0,140,69,0.05) 75%)
            `,
            backgroundSize: '100% 100%, 60px 60px, 60px 60px'
          }}
          animate={{
            backgroundPosition: [
              '0% 0%, 0px 0px, 0px 0px',
              '100% 100%, 60px 60px, -60px -60px'
            ]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* Interactive decorative shapes */}
        <div className="absolute inset-0 pointer-events-none mix-blend-plus-lighter">
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={`shape-${i}`}
              className="absolute rounded-full mix-blend-screen animate-glow"
              style={{
                width: `${80 + i * 40}px`,
                height: `${80 + i * 40}px`,
                background: `
                  radial-gradient(circle at center, 
                    rgba(0,140,69,${0.2 - i * 0.03}) 0%, 
                    rgba(0,179,89,${0.15 - i * 0.02}) 35%,
                    transparent 70%
                  )
                `,
                filter: 'blur(8px) drop-shadow(0 0 8px rgba(0,140,69,0.3))',
                top: `${15 + Math.sin(i * 1.5) * 20}%`,
                left: `${10 + Math.cos(i * 1.2) * 15}%`,
              }}
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.6, 0.9, 0.6],
                rotate: [0, 180, 360],
                y: [0, Math.sin(i * 0.8) * 30, 0],
                x: [0, Math.cos(i * 0.8) * 30, 0],
                filter: [
                  'blur(8px) brightness(1) drop-shadow(0 0 8px rgba(0,140,69,0.3))',
                  'blur(12px) brightness(1.2) drop-shadow(0 0 12px rgba(0,140,69,0.4))',
                  'blur(8px) brightness(1) drop-shadow(0 0 8px rgba(0,140,69,0.3))'
                ]
              }}
              transition={{
                duration: 15 + i * 2,
                repeat: Infinity,
                ease: "easeInOut",
                times: [0, 0.5, 1]
              }}
            />
          ))}
        </div>

        {/* Animated lines */}
        <svg className="absolute inset-0 w-full h-full opacity-20" style={{ filter: 'blur(1px)' }}>
          <motion.path
            d="M0,50 Q250,0 500,50 T1000,50"
            stroke="rgba(0,140,69,0.3)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "linear"
            }}
          />
          <motion.path
            d="M0,150 Q250,100 500,150 T1000,150"
            stroke="rgba(0,140,69,0.2)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "linear",
              delay: 0.5
            }}
          />
        </svg>
      </div>

      {/* Property Showcase Carousel */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="carousel max-w-6xl mx-auto">
          {[
            {
              id: 'property-1',
              image: '/images/property1.jpg',
              title: 'Luxury Villa in Beverly Hills',
              price: '$5,000,000',
            },
            {
              id: 'property-2',
              image: '/images/property2.jpg',
              title: 'Modern Apartment in NYC',
              price: '$2,500,000',
            },
            {
              id: 'property-3',
              image: '/images/property3.jpg',
              title: 'Cozy Cottage in the Countryside',
              price: '$850,000',
            },
          ].map((property, index) => (
            <motion.div
              key={property.id}
              className="carousel-slide relative"
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
            >
              <img
                src={property.image}
                alt={property.title}
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
              <div className="absolute inset-0 bg-black bg-opacity-30 flex flex-col justify-end p-6">
                <h3 className="text-2xl font-bold text-white">{property.title}</h3>
                <p className="text-lg text-white">{property.price}</p>
                <div className="mt-4 flex space-x-4">
                  <Link
                    to={`/properties/${property.id}`}
                    className="bg-italian-green text-white px-4 py-2 rounded-md hover:bg-italian-green-light transition"
                  >
                    View Details
                  </Link>
                  <button className="bg-white text-italian-green px-4 py-2 rounded-md hover:bg-gray-100 transition">
                    Save Property
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Enhanced bottom wave */}
      <div className="absolute bottom-0 left-0 right-0 overflow-hidden">
        <motion.div
          className="h-24 bg-gradient-to-t from-white to-transparent dark:from-gray-900 dark:to-transparent"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        />
        <motion.div
          className="absolute bottom-0 left-0 right-0 h-32"
          style={{
            background: 'linear-gradient(45deg, rgba(0,140,69,0.1) 0%, transparent 75%)',
            maskImage: 'linear-gradient(to bottom, transparent, black)'
          }}
          animate={{
            x: ['-100%', '100%']
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      {/* Particle system */}
      <canvas
        className="absolute inset-0 pointer-events-none"
        style={{ opacity: 0.1 }}
        ref={canvasRef}
      />
    </section>
  );
};

export default HeroSection;