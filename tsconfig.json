{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": false, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@core/*": ["src/core/*"], "@features/*": ["src/features/*"], "@shared/*": ["src/shared/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}