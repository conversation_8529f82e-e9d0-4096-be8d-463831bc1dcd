import{u as v,r as u,s as w,y as b,j as e}from"./index-aYgmXB_2.js";import{u as _,f as N,e as S,a as A}from"./useSupabaseQuery-1rLPVlp7.js";const h=3,f=1500,D={pending:["review","accepted","rejected","archived"],review:["accepted","rejected","archived"],accepted:["archived"],rejected:["archived"],archived:["pending"]},$=()=>{const{t:r}=v(),x=_({table:"career_applications",orderBy:{column:"created_at",ascending:!1}}),[n,l]=u.useState({applications:[],loading:!0,error:null,lastFetched:null,supabaseConnected:!1});u.useEffect(()=>{d(),m()},[]);const d=async()=>{try{console.log("Verifying Supabase connection...");const{data:i,error:t}=await w.from("career_applications").select("count");return t?(console.error("Supabase connection check failed:",t),l(s=>({...s,supabaseConnected:!1})),!1):(console.log("Supabase connection verified successfully"),l(s=>({...s,supabaseConnected:!0})),!0)}catch(i){return console.error("Failed to verify Supabase connection:",i),l(t=>({...t,supabaseConnected:!1})),!1}},m=u.useCallback(async(i=0)=>{console.log(`Fetching applications data from Supabase... (Attempt ${i+1}/${h+1})`),l(t=>({...t,loading:!0}));try{if(!await d()&&i<h){console.warn(`Supabase connection failed, retrying in ${f}ms...`),setTimeout(()=>m(i+1),f);return}console.log("Executing applications query...");const s=performance.now(),{data:c,error:o,status:y}=await w.from("career_applications").select("*").order("created_at",{ascending:!1}),j=performance.now()-s;if(console.log(`Query completed in ${j.toFixed(2)}ms with status ${y}`),o)throw console.error(`Supabase error (${y}):`,o),o;if(console.log(`Retrieved ${(c==null?void 0:c.length)||0} application records`),console.log("Sample data:",c&&c.length>0?c[0]:"No data available"),!c||!Array.isArray(c))throw console.warn("Data is not in expected format:",c),new Error("Invalid data format received from the server");const k=c.map(a=>(a.id||console.warn("Application missing ID:",a),a.email||console.warn("Application missing email:",a),{...a,id:a.id||`temp-${Date.now()}`,first_name:a.first_name?a.first_name.trim():"",last_name:a.last_name?a.last_name.trim():"",email:a.email?a.email.trim().toLowerCase():"",phone:a.phone?a.phone.trim():"",career_id:a.career_id||"",career_title:a.career_title?a.career_title.trim():"Unknown Position",resume_url:a.resume_url||"",self_presentation:a.self_presentation||"",motivation:a.motivation||"",created_at:a.created_at||new Date().toISOString(),user_id:a.user_id||"",status:a.status||"pending"}));console.log("Data processing complete, updating state..."),l(a=>({...a,applications:k,loading:!1,error:null,errorDetails:null,lastFetched:Date.now(),supabaseConnected:!0}))}catch(t){console.error("Error fetching applications:",t);let s="An error occurred while fetching application data",c=null;if(t instanceof Error?(console.error(`Error name: ${t.name}, Message: ${t.message}`),console.error("Error stack:",t.stack),s=t.message,c={name:t.name,stack:t.stack,timestamp:new Date().toISOString()}):typeof t=="object"&&t!==null&&(c=t),i<h){const o=f*(i+1);console.warn(`Retrying fetchApplications in ${o}ms... (Attempt ${i+1}/${h})`),setTimeout(()=>m(i+1),o)}else console.error("Maximum retry attempts reached. Giving up."),l(o=>({...o,loading:!1,error:s,errorDetails:c}))}},[]),p=u.useCallback(async(i,t)=>{try{const s=n.applications.find(o=>o.id===i);if(!s)throw new Error("Application not found");if(!D[s.status].includes(t))throw new Error(`Invalid status transition from ${s.status} to ${t}`);return await x.updateRecord(i,{status:t}),b.success(r("admin.applications.status.updated")),!0}catch(s){return console.error("Error updating application status:",s),b.error(r("admin.applications.status.error")),!1}},[n.applications,x]),g=()=>({connected:n.supabaseConnected,lastChecked:new Date().toISOString(),lastFetched:n.lastFetched?new Date(n.lastFetched).toISOString():null,dataAge:n.lastFetched?Date.now()-n.lastFetched:null});return{applications:n.applications,loading:n.loading,error:n.error,errorDetails:n.errorDetails,lastFetched:n.lastFetched,connectionInfo:g(),refetch:()=>m(0),updateApplicationStatus:p,checkConnection:d}},I=()=>{const{t:r,i18n:x}=v(),{applications:n,loading:l,error:d,updateApplicationStatus:m}=$(),[p,g]=u.useState(null),i=x.language==="fr"?N:S;return l?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-italian-green"})}):d?e.jsx("div",{className:"bg-red-50 dark:bg-red-900 p-4 rounded-md",children:e.jsx("p",{className:"text-red-800 dark:text-red-200",children:d})}):e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsx("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:r("admin.applications.title")})}),n.length===0?e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:r("admin.applications.list.empty")})}):e.jsx("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-900",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:r("admin.applications.table.name")}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:r("admin.applications.table.email")}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:r("admin.applications.table.position")}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:r("admin.applications.table.status")}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:r("admin.applications.table.date")}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:r("admin.applications.table.actions")})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:n.map(t=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:`${t.first_name} ${t.last_name}`})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.email})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900 dark:text-white",children:t.career_title})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsxs("select",{className:`text-sm rounded-md ${p===t.id?"bg-blue-50 dark:bg-blue-900":"bg-white dark:bg-gray-700"} border border-gray-300 dark:border-gray-600`,value:t.status||"pending",disabled:p===t.id,onChange:async s=>{try{g(t.id),await m(t.id,s.target.value)}finally{g(null)}},children:[e.jsx("option",{value:"pending",children:r("admin.applications.status.pending")}),e.jsx("option",{value:"reviewed",children:r("admin.applications.status.reviewed")}),e.jsx("option",{value:"contacted",children:r("admin.applications.status.contacted")}),e.jsx("option",{value:"rejected",children:r("admin.applications.status.rejected")})]}),p===t.id&&e.jsx("div",{className:"ml-2 animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-italian-green"})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:A(new Date(t.created_at),"PPp",{locale:i})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:e.jsx("button",{className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-4",onClick:()=>window.open(t.resume_url,"_blank"),children:r("admin.applications.actions.download")})})]},t.id))})]})})]})};export{I as default};
