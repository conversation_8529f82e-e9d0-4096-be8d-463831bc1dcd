import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistReducer, persistStore, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { propertyApi } from '../features/properties/api/propertyApi';
import themeReducer from '../core/theme/themeSlice';
import languageReducer from '../core/i18n/languageSlice';
import navigationReducer from '../core/navigation/navigationSlice';
import propertiesReducer from '../features/properties/propertiesSlice';
import filtersReducer from '../features/properties/filtersSlice';
import userPreferencesReducer from '../features/properties/userPreferencesSlice';
import agentReducer from '../features/properties/agentSlice';

// Create separate persist configs for each reducer
const themePersistConfig = {
  key: 'theme',
  storage,
};

const languagePersistConfig = {
  key: 'language',
  storage,
};

const userPreferencesPersistConfig = {
  key: 'userPreferences',
  storage,
};

const agentPersistConfig = {
  key: 'agent',
  storage,
};

const rootReducer = combineReducers({
  [propertyApi.reducerPath]: propertyApi.reducer,
  theme: persistReducer<ReturnType<typeof themeReducer>>(themePersistConfig, themeReducer),
  language: persistReducer<ReturnType<typeof languageReducer>>(languagePersistConfig, languageReducer),
  navigation: navigationReducer,
  properties: propertiesReducer,
  filters: filtersReducer,
  userPreferences: persistReducer<ReturnType<typeof userPreferencesReducer>>(userPreferencesPersistConfig, userPreferencesReducer),
  agent: persistReducer<ReturnType<typeof agentReducer>>(agentPersistConfig, agentReducer),
});

const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }).concat(propertyApi.middleware),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
