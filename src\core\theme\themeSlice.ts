import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { themes, ThemeType } from './themeConfig';
import { ThemeMode, ThemeColors } from './types';

export interface ThemeState {
  mode: 'light' | 'dark';
}

const initialState: ThemeState = {
  mode: 'light',
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    toggleTheme: (state) => {
      state.mode = state.mode === 'light' ? 'dark' : 'light';
    },
  }
});


export const { toggleTheme } = themeSlice.actions;
export default themeSlice.reducer;
