import React from 'react';
import { useTranslation } from '@/core/i18n/useTranslation';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const ThankYou: React.FC = () => {
  const { t } = useTranslation();

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-16">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-2xl mx-auto p-6"
      >
        {/* Success Icon */}
        <motion.div
          variants={itemVariants}
          className="w-20 h-20 mx-auto mb-8 bg-italian-green rounded-full flex items-center justify-center"
        >
          <svg
            className="w-10 h-10 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </motion.div>

        {/* Thank You Message */}
        <motion.div
          variants={itemVariants}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {t('career.application.thankYou')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {t('career.application.thankYouSubtitle')}
          </p>
          <div className="w-16 h-1 bg-italian-green mx-auto mb-6"></div>
          <p className="text-gray-600 dark:text-gray-300">
            {t('career.application.success')}
          </p>
        </motion.div>

        {/* Next Steps */}
        <motion.div
          variants={itemVariants}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8"
        >
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 text-center">
            {t('career.application.whatNext')}
          </h2>
          <div className="space-y-4">
            {t('career.application.nextSteps', { returnObjects: true }).map((step: string, index: number) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-start"
              >
                <div className="flex-shrink-0 w-8 h-8 bg-italian-green/10 rounded-full flex items-center justify-center mr-4">
                  <span className="text-italian-green font-semibold">{index + 1}</span>
                </div>
                <p className="text-gray-600 dark:text-gray-300 mt-1">{step}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Back Button */}
        <motion.div variants={itemVariants} className="text-center">
          <Link
            to="/career"
            className="inline-flex items-center justify-center bg-italian-green text-white px-8 py-3 rounded-md hover:bg-italian-green/90 transition-colors group"
          >
            <svg
              className="w-4 h-4 mr-2 transform group-hover:-translate-x-1 transition-transform"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            {t('career.backToCareer')}
          </Link>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default ThankYou;
