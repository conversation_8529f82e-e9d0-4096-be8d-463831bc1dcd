import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../app/types';
import Header from '../navigation/Header';
import Footer from '../navigation/Footer';
import { CookieConsent } from '../components/CookieConsent';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { mode } = useSelector((state: RootState) => state.theme);

  return (
    <div className={`min-h-screen flex flex-col ${mode === 'dark' ? 'dark' : ''}`}>
      <Header />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
      <CookieConsent />
    </div>
  );
};

export default Layout;
