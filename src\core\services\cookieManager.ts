import Cookies from 'js-cookie';

export interface CookiePreferences {
  necessary: boolean; // Always true, can't be disabled
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

export interface UserBehavior {
  lastVisit: string;
  pageViews: number;
  interactions: {
    propertyViews: string[]; // Store property IDs
    searches: string[]; // Store search queries
    favorites: string[]; // Store favorited property IDs
  };
}

const COOKIE_EXPIRY = 365; // days

export const CookieManager = {
  // Initialize cookies with default preferences
  initializeCookies(): CookiePreferences {
    const existingPrefs = this.getCookiePreferences();
    if (!existingPrefs) {
      const defaultPrefs: CookiePreferences = {
        necessary: true,
        analytics: false,
        marketing: false,
        functional: false
      };
      this.setCookiePreferences(defaultPrefs);
      return defaultPrefs;
    }
    return existingPrefs;
  },

  // Get current cookie preferences
  getCookiePreferences(): CookiePreferences | null {
    const prefs = Cookies.get('cookiePreferences');
    return prefs ? JSON.parse(prefs) : null;
  },

  // Set cookie preferences
  setCookiePreferences(preferences: CookiePreferences) {
    Cookies.set('cookiePreferences', JSON.stringify(preferences), { expires: COOKIE_EXPIRY });
  },

  // Track user behavior if analytics is enabled
  trackUserBehavior(type: keyof UserBehavior['interactions'], value: string) {
    const prefs = this.getCookiePreferences();
    if (!prefs?.analytics) return;

    const behavior = this.getUserBehavior();
    behavior.interactions[type].push(value);
    behavior.pageViews += 1;
    behavior.lastVisit = new Date().toISOString();
    
    Cookies.set('userBehavior', JSON.stringify(behavior), { expires: COOKIE_EXPIRY });
  },

  // Get tracked user behavior
  getUserBehavior(): UserBehavior {
    const behavior = Cookies.get('userBehavior');
    if (behavior) {
      return JSON.parse(behavior);
    }
    return {
      lastVisit: new Date().toISOString(),
      pageViews: 0,
      interactions: {
        propertyViews: [],
        searches: [],
        favorites: []
      }
    };
  },

  // Set marketing preferences if marketing cookies are enabled
  setMarketingPreferences(preferences: { [key: string]: any }) {
    const prefs = this.getCookiePreferences();
    if (!prefs?.marketing) return;
    
    Cookies.set('marketingPreferences', JSON.stringify(preferences), { expires: COOKIE_EXPIRY });
  },

  // Get marketing preferences
  getMarketingPreferences() {
    const prefs = Cookies.get('marketingPreferences');
    return prefs ? JSON.parse(prefs) : null;
  },

  // Set functional preferences if functional cookies are enabled
  setFunctionalPreferences(preferences: { [key: string]: any }) {
    const prefs = this.getCookiePreferences();
    if (!prefs?.functional) return;
    
    Cookies.set('functionalPreferences', JSON.stringify(preferences), { expires: COOKIE_EXPIRY });
  },

  // Clear all non-essential cookies
  clearNonEssentialCookies() {
    Cookies.remove('userBehavior');
    Cookies.remove('marketingPreferences');
    Cookies.remove('functionalPreferences');
  }
};
