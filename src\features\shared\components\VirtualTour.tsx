import React from 'react';
import { Pannellum } from 'pannellum-react';

interface VirtualTourProps {
  propertyId: string;
}

const VirtualTour: React.FC<VirtualTourProps> = ({ propertyId }) => {
  return (
    <div style={{ height: '400px', width: '100%' }}>
      <Pannellum
        width="100%"
        height="100%"
        image="/path/to/360-image.jpg"
        pitch={10}
        yaw={180}
        hfov={110}
        autoLoad
        onLoad={() => {
          console.log(`panorama for property ${propertyId} loaded`);
        }}
      />
    </div>
  );
};

export default VirtualTour;