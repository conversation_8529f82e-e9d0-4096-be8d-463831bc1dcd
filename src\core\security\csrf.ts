import axios from 'axios';

// Configure axios to include CSRF token in headers
axios.defaults.xsrfCookieName = 'XSRF-TOKEN';
axios.defaults.xsrfHeaderName = 'X-XSRF-TOKEN';

// Function to fetch CSRF token
export const fetchCsrfToken = async () => {
  try {
    await axios.get('/api/csrf-token');
  } catch (error) {
    console.error('Failed to fetch CSRF token:', error);
  }
};

// Initialize CSRF protection
export const initializeCsrf = () => {
  fetchCsrfToken();

  // Refresh token periodically
  setInterval(fetchCsrfToken, 1800000); // 30 minutes
};
