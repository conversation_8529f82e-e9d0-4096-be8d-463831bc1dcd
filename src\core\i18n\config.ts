import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { admin as enAdmin } from './locales/en/admin';
import { admin as frAdmin } from './locales/fr/admin';

// If these keys don't exist in admin files, they need to be added to those files
// This ensures applications keys are verified but keeps the actual changes in admin files
// The applications translations should be added to the respective admin files
if (!enAdmin.applications) {
  console.warn('Missing applications translations in English admin file');
  // The following structure should be added to the English admin file:
  /*
  applications: {
    title: 'Applications',
    list: {
      title: 'Job Applications',
      empty: 'No applications found',
      search: 'Search applications'
    },
    fields: {
      id: 'ID',
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      position: 'Position',
      status: 'Status',
      cv: 'CV/Resume',
      coverLetter: 'Cover Letter',
      createdAt: 'Applied On',
      updatedAt: 'Last Updated'
    },
    status: {
      new: 'New',
      reviewed: 'Reviewed',
      interviewing: 'Interviewing',
      offered: 'Offered',
      hired: 'Hired',
      rejected: 'Rejected'
    },
    actions: {
      view: 'View Details',
      download: 'Download Files',
      changeStatus: 'Change Status',
      delete: 'Delete'
    }
  }
  */
}
if (!frAdmin.applications) {
  console.warn('Missing applications translations in French admin file');
  // The following structure should be added to the French admin file:
  /*
  applications: {
    title: 'Candidatures',
    list: {
      title: 'Candidatures d\'emploi',
      empty: 'Aucune candidature trouvée',
      search: 'Rechercher des candidatures'
    },
    fields: {
      id: 'ID',
      name: 'Nom',
      email: 'Email',
      phone: 'Téléphone',
      position: 'Poste',
      status: 'Statut',
      cv: 'CV',
      coverLetter: 'Lettre de motivation',
      createdAt: 'Candidature le',
      updatedAt: 'Dernière mise à jour'
    },
    status: {
      new: 'Nouvelle',
      reviewed: 'Examinée',
      interviewing: 'Entretien',
      offered: 'Offre proposée',
      hired: 'Embauché(e)',
      rejected: 'Rejetée'
    },
    actions: {
      view: 'Voir détails',
      download: 'Télécharger les fichiers',
      changeStatus: 'Changer de statut',
      delete: 'Supprimer'
    }
  }
  */
}

// English translations
const enTranslations = {
  admin: enAdmin,
  common: {
    welcome: 'Welcome to Darden Property & Management',
    search: 'Search',
    menu: 'Menu',
    darkMode: 'Dark Mode',
    language: 'Language',
    contact: 'Contact Us',
    currency: '{{amount}} MAD', // Changed from '{{amount}} $' to '{{amount}} MAD'
    learnMore: 'Learn More',
  },
  navigation: {
    home: 'Home',
    properties: 'Properties',
    career: 'Career',
    services: 'Services',
    about: 'About',
    contact: 'Contact',
    terms: 'Terms of Service',
    privacy: 'Privacy Policy'
  },
  career: {
    title: 'Join Our Team',
    subtitle: 'Build Your Career with Darden Property & Management',
    apply: 'Apply Now',
    responsibilities: 'Key Responsibilities',
    requirements: 'Requirements',
    positions: [
      {
        title: 'Full Stack Development Intern',
        type: 'Internship',
        duration: '3-6 months',
        tasks: [
          'Assist in developing React/TypeScript frontend components',
          'Work on Node.js/Express backend services',
          'Participate in database design and implementation',
          'Write unit tests and documentation',
          'Collaborate with senior developers on feature implementation'
        ],
        requirements: [
          'Currently pursuing a degree in Computer Science or related field',
          'Basic knowledge of React and Node.js',
          'Understanding of databases (SQL/NoSQL)',
          'Good problem-solving skills',
          'Strong desire to learn and grow'
        ]
      },
      {
        title: 'Sales Intern',
        type: 'Internship',
        duration: '3-6 months',
        tasks: [
          'Assist in property showings and client meetings',
          'Support lead generation and follow-up activities',
          'Help maintain CRM database',
          'Prepare sales reports and presentations',
          'Learn about real estate market analysis'
        ],
        requirements: [
          'Currently pursuing a degree in Business, Marketing, or related field',
          'Strong communication and interpersonal skills',
          'Basic understanding of sales processes',
          'Proficiency in MS Office suite',
          'Ability to work in a fast-paced environment'
        ]
      },
      {
        title: 'Digital Marketing Intern',
        type: 'Internship',
        duration: '3-6 months',
        tasks: [
          'Assist in social media management and content creation',
          'Help with email marketing campaigns',
          'Support SEO optimization efforts',
          'Analyze marketing metrics and prepare reports',
          'Create and edit property listing content'
        ],
        requirements: [
          'Currently pursuing a degree in Marketing, Communications, or related field',
          'Knowledge of social media platforms and best practices',
          'Basic understanding of SEO and digital marketing',
          'Creative mindset with good writing skills',
          'Experience with design tools is a plus'
        ]
      },
      {
        title: 'Senior Sales Manager',
        type: 'Permanent',
        tasks: [
          'Lead and develop the sales team',
          'Establish and maintain key client relationships',
          'Develop and implement sales strategies',
          'Set and achieve sales targets',
          'Provide market analysis and forecasting',
          'Mentor junior sales staff'
        ],
        requirements: [
          '5+ years experience in real estate sales',
          'Proven track record of achieving sales targets',
          'Strong leadership and team management skills',
          'Excellent negotiation abilities',
          'Deep understanding of real estate market',
          'Valid real estate license'
        ]
      },
      {
        title: 'Property Manager',
        type: 'Permanent',
        tasks: [
          'Oversee property operations and maintenance',
          'Manage tenant relationships and lease agreements',
          'Coordinate with contractors and service providers',
          'Handle property inspections and reports',
          'Manage property budgets and financial reporting',
          'Ensure compliance with regulations'
        ],
        requirements: [
          '3+ years experience in property management',
          'Strong knowledge of property management software',
          'Excellent organizational and problem-solving skills',
          'Experience with budget management',
          'Knowledge of real estate laws and regulations',
          'Property management certification preferred'
        ]
      }
    ],
    benefits: {
      title: 'Why Join Us',
      items: [
        {
          icon: '🌟',
          title: 'Growth Opportunities',
          description: 'Continuous learning and career advancement paths'
        },
        {
          icon: '🎯',
          title: 'Competitive Package',
          description: 'Excellent compensation and benefits'
        },
        {
          icon: '🤝',
          title: 'Great Culture',
          description: 'Collaborative and inclusive work environment'
        }
      ]
    }
  },
  home: {
    hero: {
      title: 'Find Your Dream Property',
      subtitle: 'Discover exceptional properties in prime locations',
      cta: 'Start Searching',
      badge: 'Premium',
      secondaryCta: 'Learn More',
      trustBadge: 'Trusted by thousands of clients',
      highlight: 'Featured'
    },
    featured: {
      title: 'Featured Properties',
      viewAll: 'View All',
    },
    services: {
      title: 'Our Services',
      subtitle: 'Discover our comprehensive real estate solutions',
    },
    about: {
      title: 'About Us',
      subtitle: 'Learn more about our company and values',
      cta: 'Read More',
      learnMore: 'Learn More',
    }
  },
  properties: {
    title: 'Our Properties',
    subtitle: 'Explore our exclusive selection of premium properties in Morocco',
    exclusive: 'Exclusive',
    sale: 'For Sale',
    rent: 'For Rent',
    forSale: 'For Sale',
    forRent: 'For Rent',
    search: 'Search Properties',
    location: 'Location',
    type: 'Property Type',
    price: 'Price',
    all: 'All',
    studio: 'Studio',
    apartment: 'Apartment',
    office: 'Office',
    apply: 'Apply Filters',
    reset: 'Reset',
    viewDetails: 'View Details',
    viewAll: 'View All',
    viewSimilar: 'Discover Similar Properties',
    findMore: 'Find more properties like this one in our exclusive collection',
    similar: 'Similar Properties',
    similarProperties: 'Similar Properties',
    currency: 'MAD',
    noResults: 'No properties match your search criteria',
    notFound: 'Property Not Found',
    notFoundDescription: 'The property you are looking for does not exist or has been removed.',
    backToProperties: 'Back to Properties',
    features: 'Features',
    interested: 'Interested in this Property?',
    interestedText: 'Contact us to learn more about this property or schedule a viewing at your convenience.',
    schedule: 'Schedule a Visit',
    contact: 'Contact Agent',
    aboutProperty: 'About This Property',
    beds: 'Beds',
    baths: 'Baths',
    area: 'Area',
    types: {
      all: 'All Properties',
      studio: 'Studios',
      apartment: 'Apartments',
      office: 'Offices'
    },
    filters: {
      title: 'Filter Properties',
      priceRange: {
        title: 'Price Range',
        min: 'Min Price',
        max: 'Max Price'
      },
      location: {
        title: 'Location',
        placeholder: 'Enter location'
      },
      propertyType: {
        title: 'Property Type',
        apartment: 'Apartment',
        house: 'House',
        villa: 'Villa',
        office: 'Office Space',
        retail: 'Retail Space',
        land: 'Land'
      },
      listingType: {
        title: 'Listing Type',
        all: 'All Properties',
        rent: 'For Rent',
        sale: 'For Sale'
      },
      apply: 'Apply Filters',
      reset: 'Reset Filters',
      active: 'Filters Active',
      searching: 'Searching...',
    },
    loading: 'Loading...',
    propertyLocation: 'Location',
    advisor: {
      title: 'Property Advisor',
      contact: 'Contact',
      phone: 'Phone',
      email: 'Email',
    },
  },
  services: {
    title: 'Our Services',
    subtitle: 'Comprehensive Real Estate Solutions',
    learnMore: 'Learn More',
    ctaTitle: 'Ready to get started?',
    ctaText: 'Contact our professional team today to discuss how our services can meet your real estate needs.',
    propertyManagement: {
      title: 'Property Management',
      description: 'Professional management of residential and commercial properties, ensuring optimal performance and tenant satisfaction.',
      feature1: 'Tenant screening and placement',
      feature2: 'Rent collection and accounting',
      feature3: 'Property maintenance and regular inspections',
      features: {
        feature1: 'Tenant screening and placement',
        feature2: 'Rent collection and accounting',
        feature3: 'Property maintenance and regular inspections'
      }
    },
    consulting: {
      title: 'Real Estate Consulting',
      description: 'Expert advice on property investments, market analysis, and strategic planning for your real estate portfolio.',
      feature1: 'Market analysis and investment strategy',
      feature2: 'Portfolio optimization',
      feature3: 'Risk assessment and mitigation',
      features: {
        feature1: 'Market analysis and investment strategy',
        feature2: 'Portfolio optimization',
        feature3: 'Risk assessment and mitigation'
      }
    },
    rental: {
      title: 'Rental Services',
      description: 'Comprehensive rental services for both tenants and property owners, streamlining the rental process.',
      feature1: 'Property listing and tenant matching',
      feature2: 'Lease preparation and review',
      feature3: 'Move-in coordination and inspections',
      features: {
        feature1: 'Property listing and tenant matching',
        feature2: 'Lease preparation and review',
        feature3: 'Move-in coordination and inspections'
      }
    },
    process: {
      title: 'Our Process',
      subtitle: 'How we work with you from start to finish',
      step1: {
        title: 'Initial Consultation',
        description: 'We begin with a thorough discussion of your needs and goals.'
      },
      step2: {
        title: 'Custom Strategy',
        description: 'We develop a tailored plan specific to your property or investment needs.'
      },
      step3: {
        title: 'Implementation',
        description: 'Our team executes the strategy with attention to every detail.'
      },
      step4: {
        title: 'Ongoing Support',
        description: 'We provide continuous support and regular updates on performance.'
      }
    },
    testimonials: {
      title: 'Client Testimonials',
      subtitle: 'What our clients say about our services',
      testimonial1: {
        name: 'Sarah Johnson',
        role: 'Homeowner',
        content: "The property management services have been exceptional. Responsive, professional, and they've increased the value of my investment by 15% in just one year."
      },
      testimonial2: {
        name: 'Michael Chen',
        role: 'Real Estate Investor',
        content: "Their consulting expertise helped me navigate a complex commercial property acquisition. The insights saved me from a potentially costly mistake."
      },
      testimonial3: {
        name: 'Emma Williams',
        role: 'Tenant',
        content: "I found my dream apartment through their rental services. The process was smooth, transparent, and they found exactly what I was looking for within my budget."
      }
    },
    cta: {
      title: 'Ready to Elevate Your Real Estate Experience?',
      subtitle: 'Contact our professional team today to discuss how our services can meet your real estate needs.',
      button: 'Get Started'
    }
  },
  about: {
    title: 'About Us',
    subtitle: 'Building Dreams, Creating Value',
    mission: {
      title: 'Our Mission',
      description: 'To transform the real estate experience through innovation, integrity, and exceptional service, making every client\'s property journey a success story.',
    },
    vision: {
      title: 'Our Vision',
      description: 'To be the most trusted name in real estate, known for our commitment to excellence and our ability to create lasting value for our clients.',
    },
    values: {
      title: 'Our Values',
      items: [
        {
          title: 'Excellence',
          description: 'We strive for excellence in everything we do, setting the highest standards in the industry.'
        },
        {
          title: 'Integrity',
          description: 'We conduct our business with unwavering honesty and transparency.'
        },
        {
          title: 'Innovation',
          description: 'We embrace new technologies and ideas to provide cutting-edge solutions.'
        },
        {
          title: 'Client Focus',
          description: 'Our clients\' success is our success, and their satisfaction is our priority.'
        }
      ]
    },
    team: {
      title: 'Leadership Team',
      founder: {
        name: 'Dio De Nicola',
        position: 'Founder & CEO',
        quote: 'Real estate is about dreams. It\'s about turning dreams into reality.',
        description: 'With over 10 years of experience in real estate and property management, Dio leads with vision and innovation.'
      }
    },
    stats: {
      title: 'Our Impact',
      subtitle: 'Measuring our success through these impressive numbers',
      items: [
        {
          value: '10+',
          label: 'Years Experience'
        },
        {
          value: '6+',
          label: 'Happy Clients'
        },
        {
          value: '20+',
          label: 'Properties Managed'
        },
        {
          value: '98%',
          label: 'Client Satisfaction'
        }
      ]
    }
  },
  contact: {
    title: 'Contact Us',
    subtitle: 'Get in touch with our team',
    form: {
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      subject: 'Subject',
      message: 'Message',
      schedule: 'Preferred Meeting Time',
      contactPreference: 'Preferred Contact Method',
      fileUpload: 'Upload Documents (Optional)',
      submit: 'Send Message',
      success: 'Message sent successfully!',
      error: 'Error sending message. Please try again.',
    },
    info: {
      title: 'Contact Information',
      address: 'Casa Finance City (CFC), Casablanca Morocco',
      email: '<EMAIL>',
      phone: '+****************',
      hours: 'Monday - Friday: 9:00 AM - 6:00 PM',
    }
  },
  footer: {
    description: 'Your trusted partner in property management and real estate services.',
    quickLinks: 'Quick Links',
    followUs: 'Follow Us',
    privacy: 'Privacy Policy',
    terms: 'Terms of Service',
    links: {
      home: 'Home',
      properties: 'Properties',
      services: 'Services',
      about: 'About',
      contact: 'Contact'
    }
  },
  terms: {
    title: 'Terms of Service',
    lastUpdated: 'Last Updated: February 23, 2025',
    sections: [
      {
        title: 'Introduction',
        content: 'Welcome to Darden Property & Management. By using our services, you agree to these terms.'
      },
      {
        title: 'Use of Services',
        content: 'Our services are provided for your personal and commercial use subject to these terms.'
      },
      {
        title: 'User Responsibilities',
        content: 'Users must provide accurate information and maintain the confidentiality of their accounts.'
      }
    ]
  },
  privacy: {
    title: 'Privacy Policy',
    lastUpdated: 'Last Updated: February 23, 2025',
    sections: [
      {
        title: 'Information Collection',
        content: 'We collect information to provide better services to our users.'
      },
      {
        title: 'Data Usage',
        content: 'Your data is used to improve our services and provide personalized experiences.'
      },
      {
        title: 'Data Protection',
        content: 'We implement security measures to protect your personal information.'
      }
    ]
  },
  cookies: {
    message: 'We use cookies to enhance your browsing experience and analyze our traffic.',
    acceptAll: 'Accept All',
    declineAll: 'Decline All',
    preferences: 'Cookie Preferences',
    savePreferences: 'Save Preferences',
    learnMore: 'Learn More',
    necessary: {
      title: 'Necessary Cookies',
      description: 'Essential cookies that enable basic website functionality and security features.'
    },
    analytics: {
      title: 'Analytics Cookies',
      description: 'Help us understand how visitors interact with our website by collecting and reporting information anonymously.'
    },
    marketing: {
      title: 'Marketing Cookies',
      description: 'Used to track visitors across websites to display relevant advertisements and measure their effectiveness.'
    },
    functional: {
      title: 'Functional Cookies',
      description: 'Enable enhanced functionality and personalization, such as language preferences and user settings.'
    }
  },
};

// French translations
const frTranslations = {
  admin: frAdmin,
  common: {
    welcome: 'Bienvenue à Darden Property & Management',
    search: 'Rechercher',
    menu: 'Menu',
    darkMode: 'Mode Sombre',
    language: 'Langue',
    contact: 'Contactez-nous',
    currency: '{{amount}} Dh', // Changed from '{{amount}} $' to '{{amount}} Dh'
    learnMore: 'En Savoir Plus',
  },
  navigation: {
    home: 'Accueil',
    properties: 'Propriétés',
    career: 'Carrière',
    services: 'Services',
    about: 'À Propos',
    contact: 'Contact',
    terms: 'Conditions d\'Utilisation',
    privacy: 'Politique de Confidentialité'
  },
  career: {
    title: 'Rejoignez Notre Équipe',
    subtitle: 'Construisez Votre Carrière avec Darden Property & Management',
    apply: 'Postuler Maintenant',
    responsibilities: 'Responsabilités Principales',
    requirements: 'Prérequis',
    positions: [
      {
        title: 'Stagiaire en Développement Full Stack',
        type: 'Stage',
        duration: '3-6 mois',
        tasks: [
          'Aider au développement de composants frontend React/TypeScript',
          'Travailler sur les services backend Node.js/Express',
          'Participer à la conception et à l\'implémentation de bases de données',
          'Rédiger des tests unitaires et de la documentation',
          'Collaborer avec les développeurs seniors sur l\'implémentation des fonctionnalités'
        ],
        requirements: [
          'Actuellement en cours d\'études en Informatique ou domaine connexe',
          'Connaissance de base de React et Node.js',
          'Compréhension des bases de données (SQL/NoSQL)',
          'Bonnes capacités de résolution de problèmes',
          'Fort désir d\'apprendre et de progresser'
        ]
      },
      {
        title: 'Stagiaire en Vente',
        type: 'Stage',
        duration: '3-6 mois',
        tasks: [
          'Assister aux visites de propriétés et aux réunions clients',
          'Soutenir les activités de génération et de suivi des leads',
          'Aider à maintenir la base de données CRM',
          'Préparer des rapports et présentations de vente',
          'Apprendre l\'analyse du marché immobilier'
        ],
        requirements: [
          'Actuellement en cours d\'études en Commerce, Marketing ou domaine connexe',
          'Fortes compétences en communication et relations interpersonnelles',
          'Compréhension de base des processus de vente',
          'Maîtrise de la suite MS Office',
          'Capacité à travailler dans un environnement dynamique'
        ]
      },
      {
        title: 'Stagiaire en Marketing Digital',
        type: 'Stage',
        duration: '3-6 mois',
        tasks: [
          'Assister à la gestion des réseaux sociaux et à la création de contenu',
          'Aider aux campagnes de marketing par email',
          'Soutenir les efforts d\'optimisation SEO',
          'Analyser les métriques marketing et préparer des rapports',
          'Créer et éditer le contenu des annonces immobilières'
        ],
        requirements: [
          'Actuellement en cours d\'études en Marketing, Communication ou domaine connexe',
          'Connaissance des plateformes de médias sociaux et des meilleures pratiques',
          'Compréhension de base du SEO et du marketing digital',
          'Esprit créatif avec de bonnes compétences rédactionnelles',
          'Expérience avec les outils de design est un plus'
        ]
      },
      {
        title: 'Directeur Commercial Senior',
        type: 'Permanent',
        tasks: [
          'Diriger et développer l\'équipe commerciale',
          'Établir et maintenir des relations clients clés',
          'Développer et mettre en œuvre des stratégies de vente',
          'Définir et atteindre les objectifs de vente',
          'Fournir des analyses et prévisions de marché',
          'Mentor junior sales staff'
        ],
        requirements: [
          '5+ ans d\'expérience dans la vente immobilière',
          'Historique prouvé d\'atteinte des objectifs de vente',
          'Fortes compétences en leadership et gestion d\'équipe',
          'Excellentes capacités de négociation',
          'Compréhension approfondie du marché immobilier',
          'Licence immobilière valide'
        ]
      },
      {
        title: 'Gestionnaire de Propriété',
        type: 'Permanent',
        tasks: [
          'Superviser les opérations et la maintenance des propriétés',
          'Gérer les relations avec les locataires et les contrats de location',
          'Coordonner avec les entrepreneurs et les prestataires de services',
          'Gérer les inspections et les rapports de propriété',
          'Gérer les budgets et les rapports financiers des propriétés',
          'Assurer la conformité aux réglementations'
        ],
        requirements: [
          '3+ ans d\'expérience en gestion immobilière',
          'Solide connaissance des logiciels de gestion immobilière',
          'Excellentes compétences organisationnelles et de résolution de problèmes',
          'Expérience en gestion budgétaire',
          'Connaissance des lois et réglementations immobilières',
          'Certification en gestion immobilière préférée'
        ]
      }
    ],
    benefits: {
      title: 'Pourquoi Nous Rejoindre',
      items: [
        {
          icon: '🌟',
          title: 'Opportunités de Croissance',
          description: 'Apprentissage continu et parcours d\'évolution de carrière'
        },
        {
          icon: '🎯',
          title: 'Package Compétitif',
          description: 'Excellente rémunération et avantages'
        },
        {
          icon: '🤝',
          title: 'Culture d\'Entreprise',
          description: 'Environnement de travail collaboratif et inclusif'
        }
      ]
    }
  },
  home: {
    hero: {
      title: 'Trouvez Votre Propriété de Rêve',
      subtitle: 'Découvrez des propriétés exceptionnelles dans des emplacements privilégiés',
      cta: 'Commencer la Recherche',
      badge: 'Premium',
      secondaryCta: 'En Savoir Plus',
      trustBadge: 'Fait confiance par des milliers de clients',
      highlight: 'En Vedette'
    },
    featured: {
      title: 'Propriétés en Vedette',
      viewAll: 'Voir Tout',
    },
    services: {
      title: 'Nos Services',
      subtitle: 'Découvrez nos solutions immobilières complètes',
    },
    about: {
      title: 'À Propos de Nous',
      subtitle: 'En savoir plus sur notre entreprise et nos valeurs',
      cta: 'Lire Plus',
      learnMore: 'En Savoir Plus',
    }
  },
  properties: {
    title: 'Nos Propriétés',
    subtitle: 'Explorez notre sélection exclusive de propriétés de luxe au Maroc',
    exclusive: 'Exclusif',
    sale: 'À Vendre',
    rent: 'À Louer',
    forSale: 'À Vendre',
    forRent: 'À Louer',
    search: 'Rechercher des Propriétés',
    location: 'Emplacement',
    type: 'Type de Propriété',
    price: 'Prix',
    all: 'Tout',
    studio: 'Studio',
    apartment: 'Appartement',
    office: 'Bureau',
    apply: 'Appliquer les Filtres',
    reset: 'Réinitialiser',
    viewDetails: 'Voir les Détails',
    viewAll: 'Voir Tout',
    viewSimilar: 'Découvrir des Propriétés Similaires',
    findMore: 'Trouvez plus de propriétés comme celle-ci dans notre collection exclusive',
    similar: 'Propriétés Similaires',
    similarProperties: 'Propriétés Similaires',
    currency: 'Dh',
    noResults: 'Aucune propriété ne correspond à vos critères de recherche',
    notFound: 'Propriété Non Trouvée',
    notFoundDescription: 'La propriété que vous recherchez n\'existe pas ou a été supprimée.',
    backToProperties: 'Retour aux Propriétés',
    features: 'Caractéristiques',
    interested: 'Intéressé par cette Propriété?',
    interestedText: 'Contactez-nous pour en savoir plus sur cette propriété ou planifier une visite à votre convenance.',
    schedule: 'Planifier une Visite',
    contact: 'Contacter l\'Agent',
    aboutProperty: 'À Propos de Cette Propriété',
    beds: 'Chambres',
    baths: 'Salles de Bain',
    area: 'Surface',
    types: {
      all: 'Toutes les Propriétés',
      studio: 'Studios',
      apartment: 'Appartements',
      office: 'Bureaux'
    },
    filters: {
      title: 'Filtrer les Propriétés',
      priceRange: {
        title: 'Fourchette de Prix',
        min: 'Prix Minimum',
        max: 'Prix Maximum'
      },
      location: {
        title: 'Emplacement',
        placeholder: 'Entrer l\'emplacement'
      },
      propertyType: {
        title: 'Type de Propriété',
        apartment: 'Appartement',
        house: 'Maison',
        villa: 'Villa',
        office: 'Espace de Bureau',
        retail: 'Espace Commercial',
        land: 'Terrain'
      },
      listingType: {
        title: 'Type d\'Annonce',
        all: 'Toutes les Propriétés',
        rent: 'À Louer',
        sale: 'À Vendre'
      },
      apply: 'Appliquer les Filtres',
      reset: 'Réinitialiser les Filtres',
      active: 'Filtres Actifs',
      searching: 'Recherche en cours...',
    },
    loading: 'Chargement...',
    propertyLocation: 'Emplacement',
    advisor: {
      title: 'Conseiller Immobilier',
      contact: 'Contact',
      phone: 'Téléphone',
      email: 'Email',
    },
  },
  services: {
    title: 'Nos Services',
    subtitle: 'Solutions Immobilières Complètes',
    learnMore: 'En savoir plus',
    ctaTitle: 'Prêt à commencer?',
    ctaText: 'Contactez notre équipe professionnelle aujourd\'hui pour discuter de la manière dont nos services peuvent répondre à vos besoins immobiliers.',
    propertyManagement: {
      title: 'Gestion Immobilière',
      description: 'Gestion professionnelle de biens résidentiels et commerciaux, assurant une performance optimale et la satisfaction des locataires.',
      feature1: 'Sélection et placement des locataires',
      feature2: 'Collecte des loyers et comptabilité',
      feature3: 'Maintenance des propriétés et inspections régulières',
      features: {
        feature1: 'Sélection et placement des locataires',
        feature2: 'Collecte des loyers et comptabilité',
        feature3: 'Maintenance des propriétés et inspections régulières'
      }
    },
    consulting: {
      title: 'Conseil Immobilier',
      description: 'Conseils d\'experts en investissement immobilier, analyse de marché et planification stratégique pour votre portefeuille immobilier.',
      feature1: 'Analyse de marché et stratégie d\'investissement',
      feature2: 'Optimisation du portefeuille',
      feature3: 'Évaluation et mitigation des risques',
      features: {
        feature1: 'Analyse de marché et stratégie d\'investissement',
        feature2: 'Optimisation du portefeuille',
        feature3: 'Évaluation et mitigation des risques'
      }
    },
    rental: {
      title: 'Services de Location',
      description: 'Services de location complets pour les locataires et les propriétaires, simplifiant le processus de location.',
      feature1: 'Annonce des biens et mise en relation des locataires',
      feature2: 'Préparation et gestion des baux',
      feature3: 'Coordination des visites et inspections',
      features: {
        feature1: 'Annonce des biens et mise en relation des locataires',
        feature2: 'Préparation et gestion des baux',
        feature3: 'Coordination des visites et inspections'
      }
    },
    process: {
      title: 'Notre Processus',
      subtitle: 'Comment nous travaillons avec vous du début à la fin',
      step1: {
        title: 'Consultation Initiale',
        description: 'Nous commençons par une discussion approfondie de vos besoins et objectifs.'
      },
      step2: {
        title: 'Stratégie Personnalisée',
        description: 'Nous développons un plan adapté à vos besoins spécifiques en propriété ou investissement.'
      },
      step3: {
        title: 'Mise en Œuvre',
        description: 'Notre équipe met en œuvre la stratégie avec attention à chaque détail.'
      },
      step4: {
        title: 'Soutien Continu',
        description: 'Nous fournissons un soutien continu et des mises à jour régulières sur les performances.'
      }
    },
    testimonials: {
      title: 'Témoignages de Clients',
      subtitle: 'Ce que nos clients disent de nos services',
      testimonial1: {
        name: 'Sarah Johnson',
        role: 'Propriétaire',
        content: "Les services de gestion immobilière ont été exceptionnels. Réactifs, professionnels, et ils ont augmenté la valeur de mon investissement de 15% en seulement un an."
      },
      testimonial2: {
        name: 'Michael Chen',
        role: 'Investisseur Immobilier',
        content: "Leur expertise en conseil m'a aidé à naviguer une acquisition immobilière commerciale complexe. Les insights m'ont sauvé d'une erreur potentiellement coûteuse."
      },
      testimonial3: {
        name: 'Emma Williams',
        role: 'Locataire',
        content: "J'ai trouvé mon appartement de rêve grâce à leurs services de location. Le processus était fluide, transparent, et ils ont trouvé exactement ce que je cherchais dans mon budget."
      }
    },
    cta: {
      title: 'Prêt à Élever Votre Expérience Immobilière?',
      subtitle: 'Contactez notre équipe professionnelle aujourd\'hui pour discuter de la manière dont nos services peuvent répondre à vos besoins immobiliers.',
      button: 'Commencer'
    }
  },
  about: {
    title: 'À Propos de Nous',
    subtitle: 'Construire des Rêves, Créer de la Valeur',
    mission: {
      title: 'Notre Mission',
      description: 'Transformer l\'expérience immobilière grâce à l\'innovation, l\'intégrité et un service exceptionnel, faisant de chaque parcours client une histoire à succès.',
    },
    vision: {
      title: 'Notre Vision',
      description: 'Être le nom le plus fiable de l\'immobilier, reconnu pour notre engagement envers l\'excellence et notre capacité à créer une valeur durable pour nos clients.',
    },
    values: {
      title: 'Nos Valeurs',
      items: [
        {
          title: 'Excellence',
          description: 'Nous visons l\'excellence dans tout ce que nous faisons, établissant les plus hauts standards de l\'industrie.'
        },
        {
          title: 'Intégrité',
          description: 'Nous menons nos activités avec une honnêteté et une transparence inébranlables.'
        },
        {
          title: 'Innovation',
          description: 'Nous adoptons les nouvelles technologies et idées pour fournir des solutions de pointe.'
        },
        {
          title: 'Focus Client',
          description: 'Le succès de nos clients est notre succès, et leur satisfaction est notre priorité.'
        }
      ]
    },
    team: {
      title: 'Équipe de Direction',
      founder: {
        name: 'Dio De Nicola',
        position: 'Fondateur & PDG',
        quote: 'L\'immobilier, c\'est une histoire de rêves. C\'est transformer les rêves en réalité.',
        description: 'Avec plus de 10 ans d\'expérience dans l\'immobilier et la gestion de propriétés, Dio dirige avec vision et innovation.'
      }
    },
    stats: {
      title: 'Notre Impact',
      subtitle: 'Mesurer notre succès à travers ces chiffres impressionnants',
      items: [
        {
          value: '10+',
          label: 'Années d\'Expérience'
        },
        {
          value: '6+',
          label: 'Clients Satisfaits'
        },
        {
          value: '20+',
          label: 'Propriétés Gérées'
        },
        {
          value: '98%',
          label: 'Satisfaction Client'
        }
      ]
    }
  },
  contact: {
    title: 'Contactez-nous',
    subtitle: 'Entrez en contact avec notre équipe',
    form: {
      name: 'Nom',
      email: 'Email',
      phone: 'Téléphone',
      subject: 'Sujet',
      message: 'Message',
      schedule: 'Horaire de Rendez-vous Préféré',
      contactPreference: 'Méthode de Contact Préférée',
      fileUpload: 'Télécharger des Documents (Optionnel)',
      submit: 'Envoyer le Message',
      success: 'Message envoyé avec succès !',
      error: 'Erreur lors de l\'envoi du message. Veuillez réessayer.',
    },
    info: {
      title: 'Informations de Contact',
      address: 'City Financière Casablanca (CFC), Casablanca Maroc',
      email: '<EMAIL>',
      phone: '+****************',
      hours: 'Lundi - Vendredi: 9h00 - 18h00',
    }
  },
  footer: {
    description: 'Votre partenaire de confiance en gestion immobilière et services immobiliers.',
    quickLinks: 'Liens Rapides',
    followUs: 'Suivez-nous',
    privacy: 'Politique de Confidentialité',
    terms: 'Conditions d\'Utilisation',
    links: {
      home: 'Accueil',
      properties: 'Propriétés',
      services: 'Services',
      about: 'À Propos',
      contact: 'Contact'
    }
  },
  terms: {
    title: 'Conditions d\'Utilisation',
    lastUpdated: 'Dernière mise à jour: 23 février 2025',
    sections: [
      {
        title: 'Introduction',
        content: 'Bienvenue chez Darden Property & Management. En utilisant nos services, vous acceptez ces conditions.'
      },
      {
        title: 'Utilisation des Services',
        content: 'Nos services sont fournis pour votre usage personnel et commercial sous réserve de ces conditions.'
      },
      {
        title: 'Responsabilités de l\'Utilisateur',
        content: 'Les utilisateurs doivent fournir des informations exactes et maintenir la confidentialité de leurs comptes.'
      }
    ]
  },
  privacy: {
    title: 'Politique de Confidentialité',
    lastUpdated: 'Dernière mise à jour: 23 février 2025',
    sections: [
      {
        title: 'Collecte d\'Informations',
        content: 'Nous collectons des informations pour fournir de meilleurs services à nos utilisateurs.'
      },
      {
        title: 'Utilisation des Données',
        content: 'Vos données sont utilisées pour améliorer nos services et fournir des expériences personnalisées.'
      },
      {
        title: 'Protection des Données',
        content: 'Nous mettons en place des mesures de sécurité pour protéger vos informations personnelles.'
      }
    ]
  },
  cookies: {
    message: 'Nous utilisons des cookies pour améliorer votre expérience de navigation et analyser notre trafic.',
    acceptAll: 'Tout accepter',
    declineAll: 'Tout refuser',
    preferences: 'Préférences des cookies',
    savePreferences: 'Enregistrer les préférences',
    learnMore: 'En savoir plus',
    necessary: {
      title: 'Cookies Nécessaires',
      description: 'Cookies essentiels permettant les fonctionnalités de base du site et ses fonctions de sécurité.'
    },
    analytics: {
      title: 'Cookies Analytiques',
      description: 'Nous aident à comprendre comment les visiteurs interagissent avec notre site en collectant et rapportant des informations de manière anonyme.'
    },
    marketing: {
      title: 'Cookies Marketing',
      description: 'Utilisés pour suivre les visiteurs sur les sites web afin d\'afficher des publicités pertinentes et mesurer leur efficacité.'
    },
    functional: {
      title: 'Cookies Fonctionnels',
      description: 'Permettent des fonctionnalités et une personnalisation améliorées, comme les préférences de langue et les paramètres utilisateur.'
    }
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: enTranslations },
      fr: { translation: frTranslations },
    },
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
