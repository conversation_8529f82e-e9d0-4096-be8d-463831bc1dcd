import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface NavigationState {
  currentPath: string;
  previousPath: string | null;
  isMenuOpen: boolean;
}

const initialState: NavigationState = {
  currentPath: '/',
  previousPath: null,
  isMenuOpen: false,
};

const navigationSlice = createSlice({
  name: 'navigation',
  initialState,
  reducers: {
    setCurrentPath: (state, action: PayloadAction<string>) => {
      state.previousPath = state.currentPath;
      state.currentPath = action.payload;
    },
    toggleMenu: (state) => {
      state.isMenuOpen = !state.isMenuOpen;
    },
    setMenuOpen: (state, action: PayloadAction<boolean>) => {
      state.isMenuOpen = action.payload;
    },
    goBack: (state) => {
      if (state.previousPath) {
        const temp = state.currentPath;
        state.currentPath = state.previousPath;
        state.previousPath = temp;
      }
    },
  },
});

export const { setCurrentPath, toggleMenu, setMenuOpen, goBack } = navigationSlice.actions;
export default navigationSlice.reducer;
