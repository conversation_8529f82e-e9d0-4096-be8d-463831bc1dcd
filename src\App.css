/* Custom styles that go beyond Tailwind */

/* Italian theme colors */
:root {
  --italian-green: #008C45;
  --italian-white: #F4F5F0;
  --italian-red: #CD212A;
}

/* Moroccan theme colors */
.theme-moroccan {
  --moroccan-red: #C1272D;
  --moroccan-green: #006233;
}

/* Common components */
.link-hover {
  @apply transition-colors duration-200 hover:text-italian-green dark:hover:text-italian-white;
}

.section-padding {
  @apply py-12 md:py-16 lg:py-20;
}

/* Animations */
.fade-in {
  @apply transition-opacity duration-300;
}

/* Smooth page transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}
.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}
.page-transition-exit-active {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Skeleton loading states */
.skeleton {
  @apply bg-gray-200 dark:bg-gray-700 rounded-md;
  position: relative;
  overflow: hidden;
}
.skeleton::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}
@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Hover effects for interactive elements */
.hover-effect {
  @apply transition-transform duration-200 ease-in-out;
}
.hover-effect:hover {
  transform: scale(1.05);
}

/* Loading spinners */
.spinner {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 3px solid transparent;
  border-top-color: var(--italian-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

/* Micro-interactions for buttons and links */
.button-interaction {
  @apply transition-transform duration-200 ease-in-out;
}
.button-interaction:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.button-interaction:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive typography */
@screen sm {
  h1 {
    @apply text-4xl;
  }
  h2 {
    @apply text-3xl;
  }
  h3 {
    @apply text-2xl;
  }
}

@screen lg {
  h1 {
    @apply text-5xl;
  }
  h2 {
    @apply text-4xl;
  }
  h3 {
    @apply text-3xl;
  }
}

/* Dark mode adjustments */
.dark .bg-pattern {
  opacity: 0.1;
  transition: opacity 0.3s ease, background-color 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  @apply bg-italian-green dark:bg-italian-red rounded-full;
  border: 2px solid transparent;
  background-clip: padding-box;
}