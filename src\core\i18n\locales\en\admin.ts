export const admin = {
  breadcrumbs: {
    contact: 'Contact',
    admin: 'Admin',
    contacts: 'Messages',
    applications: 'Applications',
    dashboard: 'Dashboard',
  },
  nav: {
    dashboard: 'Dashboard',
    applications: 'Applications',
    contacts: 'Messages',
    careers: 'Careers',
    properties: 'Properties',
  },
  auth: {
    login: {
      title: 'Sign in to your account',
      email: 'Email address',
      password: 'Password',
      rememberMe: 'Remember me',
      submit: 'Sign in',
      error: 'Invalid email or password',
    },
  },
  dashboard: {
    title: 'Dashboard',
    totalProperties: 'Total Properties',
    newApplications: 'New Applications',
    unreadMessages: 'Unread Messages',
    activePositions: 'Active Positions',
    recentActivity: 'Recent Activity',
    activityPlaceholder: 'No recent activity',
  },
  // Enhanced applications translations
  applications: {
    title: 'Applications',
    empty: 'No applications found',
    table: {
      name: 'Name',
      email: 'Email',
      position: 'Position',
      date: 'Date',
      actions: 'Actions',
    },
    actions: {
      viewResume: 'View Resume',
      download: 'Download Files',
      changeStatus: 'Change Status',
      delete: 'Delete'
    },
    status: {
      pending: 'Pending',
      review: 'Under Review',
      accepted: 'Accepted',
      rejected: 'Rejected',
      archived: 'Archived',
      updated: 'Status updated successfully'
    },
    errors: {
      notFound: 'Application not found',
      invalidTransition: 'Invalid status transition',
      updateFailed: 'Failed to update application status'
    },
    fields: {
      id: 'ID',
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      position: 'Position',
      status: 'Status',
      cv: 'CV/Resume',
      coverLetter: 'Cover Letter',
      createdAt: 'Applied On',
      updatedAt: 'Last Updated'
    },
    list: {
      title: 'Job Applications',
      empty: 'No applications found',
      search: 'Search applications'
    }
  },
  // Enhanced contacts translations
  contacts: {
    title: 'Contact Messages',
    empty: 'No messages found',
    table: {
      name: 'Name',
      email: 'Email',
      subject: 'Subject',
      message: 'Message',
      date: 'Date',
      actions: 'Actions',
    },
    actions: {
      view: 'View',
      delete: 'Delete',
      archive: 'Archive',
      reply: 'Reply'
    },
    status: {
      new: 'New',
      read: 'Read',
      replied: 'Replied',
      archived: 'Archived',
      updated: 'Status updated successfully',
      error: 'Failed to update status'
    },
    errors: {
      notFound: 'Message not found',
      invalidTransition: 'Invalid status transition'
    },
    fields: {
      id: 'ID',
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      subject: 'Subject',
      message: 'Message',
      attachment: 'Attachment',
      createdAt: 'Received On',
      status: 'Status'
    }
  },
  // Common admin translations
  common: {
    loading: 'Loading...',
    error: 'An error occurred',
    retry: 'Retry',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    view: 'View',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    actions: 'Actions',
    status: 'Status',
    date: 'Date',
    name: 'Name',
    email: 'Email',
    phone: 'Phone',
    subject: 'Subject',
    message: 'Message',
    position: 'Position',
    from: 'From',
    close: 'Close',
    viewAttachment: 'View Attachment',
    viewResume: 'View Resume',
    allStatuses: 'All Statuses',
    statusNew: 'New',
    statusRead: 'Read',
    statusReplied: 'Replied',
    statusArchived: 'Archived',
    statusPending: 'Pending',
    statusAccepted: 'Accepted',
    statusRejected: 'Rejected',
    statusReview: 'Under Review'
  }
} as const;