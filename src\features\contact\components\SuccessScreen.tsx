import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

interface SuccessScreenProps {
  onNewMessage: () => void;
}

const SuccessScreen: React.FC<SuccessScreenProps> = ({ onNewMessage }) => {
  const { t } = useTranslation();

  return (
    <motion.div
      className="text-center py-16"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="mb-8">
        <motion.div
          className="w-20 h-20 bg-italian-green rounded-full mx-auto flex items-center justify-center"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        >
          <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </motion.div>
      </div>

      <motion.h2
        className="text-3xl font-bold mb-4 text-gray-900 dark:text-white"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        {t('contact.form.success.title')}
      </motion.h2>

      <motion.p
        className="text-gray-600 dark:text-gray-300 mb-8 max-w-md mx-auto"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        {t('contact.form.success.message')}
      </motion.p>

      <motion.div
        className="flex flex-col sm:flex-row gap-4 justify-center"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <button
          onClick={onNewMessage}
          className="px-6 py-3 bg-italian-green text-white rounded-lg hover:bg-italian-green-dark transition-colors duration-200"
        >
          {t('contact.form.success.newMessage')}
        </button>
        <button
          onClick={() => window.location.href = '/'}
          className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-white rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
        >
          {t('contact.form.success.backHome')}
        </button>
      </motion.div>
    </motion.div>
  );
};

export default SuccessScreen;
