import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import TypewriterText from '../../contact/components/TypewriterText';

const About: React.FC = () => {
  const { t } = useTranslation();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  const values = t('about.values.items', { returnObjects: true }) || [
    { title: 'Excellence', description: 'We strive for excellence in everything we do.' },
    { title: 'Integrity', description: 'We conduct our business with unwavering honesty.' },
    { title: 'Innovation', description: 'We embrace new technologies and ideas.' },
    { title: 'Client Focus', description: 'Our clients\' success is our success.' }
  ];

  const stats = t('about.stats.items', { returnObjects: true }) || [
    { value: '15+', label: 'Years Experience' },
    { value: '1000+', label: 'Happy Clients' },
    { value: '500+', label: 'Properties Managed' },
    { value: '98%', label: 'Client Satisfaction' }
  ];

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen py-16">
      <div className="container mx-auto px-4">
        {/* Decorative Elements */}
        <div className="absolute top-40 left-10 w-24 h-24 bg-italian-green/20 rounded-full blur-3xl -z-10"></div>
        <div className="absolute top-80 right-20 w-32 h-32 bg-italian-red/20 rounded-full blur-3xl -z-10"></div>
        
        {/* Header */}
        <div className="relative">
          <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-italian-green/10 text-italian-green text-xs font-semibold px-3 py-1 rounded-full">
            {t('navigation.about')}
          </span>
        </div>
        
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            <TypewriterText text={t('about.title')} delay={70} />
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            <TypewriterText text={t('about.subtitle')} delay={50} />
          </p>
        </motion.div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <motion.div
            className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="h-2 bg-italian-red"></div>
            <div className="p-8">
              <motion.h2 
                className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center"
                variants={itemVariants}
              >
                <span className="w-8 h-8 bg-italian-red/10 rounded-full flex items-center justify-center mr-3">
                  <span className="text-italian-red text-lg">🎯</span>
                </span>
                <TypewriterText text={t('about.mission.title')} delay={50} />
              </motion.h2>
              <motion.p 
                className="text-gray-600 dark:text-gray-300"
                variants={itemVariants}
              >
                <TypewriterText text={t('about.mission.description')} delay={30} />
              </motion.p>
            </div>
          </motion.div>

          <motion.div
            className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="h-2 bg-italian-green"></div>
            <div className="p-8">
              <motion.h2 
                className="text-2xl font-bold text-gray-900 dark:text-white mb-4 flex items-center"
                variants={itemVariants}
              >
                <span className="w-8 h-8 bg-italian-green/10 rounded-full flex items-center justify-center mr-3">
                  <span className="text-italian-green text-lg">👁️</span>
                </span>
                <TypewriterText text={t('about.vision.title')} delay={50} />
              </motion.h2>
              <motion.p 
                className="text-gray-600 dark:text-gray-300"
                variants={itemVariants}
              >
                <TypewriterText text={t('about.vision.description')} delay={30} />
              </motion.p>
            </div>
          </motion.div>
        </div>

        {/* Values */}
        <motion.section
          className="mb-16"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h2 
            className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12 relative"
            variants={itemVariants}
          >
            <span className="inline-block relative">
              <span className="absolute -bottom-3 left-0 w-full h-1 bg-gradient-to-r from-italian-green to-italian-red"></span>
              <TypewriterText text={t('about.values.title')} delay={50} />
            </span>
          </motion.h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value: any, index: number) => (
              <motion.div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-t-4 border-italian-green"
                variants={itemVariants}
                style={{borderColor: index % 2 === 0 ? 'var(--color-primary)' : 'var(--color-secondary)'}}
              >
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">{value.title}</h3>
                <p className="text-gray-600 dark:text-gray-300">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Stats */}
        <motion.section
          className="mb-16 relative overflow-hidden"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-italian-green/10 to-italian-red/10 rounded-xl"></div>
          <div className="relative p-8 md:p-12 rounded-xl">
            <motion.h2 
              className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12"
              variants={itemVariants}
            >
              <TypewriterText text={t('about.stats.title')} delay={50} />
            </motion.h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              {stats.map((stat: any, index: number) => (
                <motion.div
                  key={index}
                  className="p-4 bg-white bg-opacity-80 dark:bg-gray-800 dark:bg-opacity-80 rounded-lg shadow-lg backdrop-blur-sm"
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                >
                  <h3 className="text-3xl md:text-4xl font-bold text-italian-green mb-2" style={{color: index % 2 === 0 ? 'var(--color-primary)' : 'var(--color-secondary)'}}>
                    {stat.value}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 font-medium">{stat.label}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.section>

        {/* Team */}
        <motion.section
          className="mb-16"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h2 
            className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12 relative"
            variants={itemVariants}
          >
            <span className="inline-block relative">
              <span className="absolute -bottom-3 left-0 w-full h-1 bg-gradient-to-r from-italian-green to-italian-red"></span>
              <TypewriterText text={t('about.team.title')} delay={50} />
            </span>
          </motion.h2>
          
          <motion.div 
            className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 max-w-4xl mx-auto relative overflow-hidden group"
            variants={itemVariants}
            whileHover={{ scale: 1.02 }}
          >
            <div className="absolute top-0 right-0 w-40 h-40 bg-italian-green/10 rounded-full translate-x-20 -translate-y-20 group-hover:scale-150 transition-all duration-700"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-italian-red/10 rounded-full -translate-x-20 translate-y-20 group-hover:scale-150 transition-all duration-700"></div>
            
            <div className="relative flex flex-col md:flex-row items-center md:items-start gap-8">
              <div className="w-36 h-36 rounded-full overflow-hidden border-4 border-italian-green/20 shadow-lg flex-shrink-0">
                <img 
                  src="https://source.unsplash.com/1000x1000/?businessman" 
                  alt={t('about.team.founder.name')} 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1 text-center md:text-left">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{t('about.team.founder.name')}</h3>
                <p className="text-lg text-italian-green font-medium mb-4">{t('about.team.founder.position')}</p>
                <blockquote className="text-gray-600 dark:text-gray-300 italic mb-4 border-l-4 border-italian-red pl-4">
                  "{t('about.team.founder.quote')}"
                </blockquote>
                <p className="text-gray-600 dark:text-gray-300">{t('about.team.founder.description')}</p>
              </div>
            </div>
          </motion.div>
        </motion.section>
      </div>
    </div>
  );
};

export default About;
