import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface UserPreferencesState {
  recentSearches: string[];
  favoriteProperties: string[];
  viewMode: 'grid' | 'list';
  sortBy: 'price-asc' | 'price-desc' | 'date-newest' | 'date-oldest';
}

const initialState: UserPreferencesState = {
  recentSearches: [],
  favoriteProperties: [],
  viewMode: 'grid',
  sortBy: 'date-newest',
};

const userPreferencesSlice = createSlice({
  name: 'userPreferences',
  initialState,
  reducers: {
    addRecentSearch: (state, action: PayloadAction<string>) => {
      if (!state.recentSearches.includes(action.payload)) {
        state.recentSearches = [action.payload, ...state.recentSearches.slice(0, 4)];
      }
    },
    toggleFavoriteProperty: (state, action: PayloadAction<string>) => {
      const propertyId = action.payload;
      const index = state.favoriteProperties.indexOf(propertyId);
      if (index === -1) {
        state.favoriteProperties.push(propertyId);
      } else {
        state.favoriteProperties.splice(index, 1);
      }
    },
    setViewMode: (state, action: PayloadAction<'grid' | 'list'>) => {
      state.viewMode = action.payload;
    },
    setSortBy: (state, action: PayloadAction<'price-asc' | 'price-desc' | 'date-newest' | 'date-oldest'>) => {
      state.sortBy = action.payload;
    },
    clearRecentSearches: (state) => {
      state.recentSearches = [];
    },
  },
});

export const {
  addRecentSearch,
  toggleFavoriteProperty,
  setViewMode,
  setSortBy,
  clearRecentSearches,
} = userPreferencesSlice.actions;

export default userPreferencesSlice.reducer;
