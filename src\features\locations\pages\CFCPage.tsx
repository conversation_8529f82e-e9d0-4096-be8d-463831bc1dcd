import React from 'react';
import { useTranslation } from 'react-i18next';
import LocationGallery from '../components/LocationGallery';
import LocationDetails from '../components/LocationDetails';
import LocationMap from '../components/LocationMap';

const CFCPage: React.FC = () => {
  const { t } = useTranslation();
  const locations = t('locations.items', { returnObjects: true }) as any[];
  const cfcLocation = locations.find((location) => location.id === 'cfc');

  if (!cfcLocation) {
    return <div>Location not found</div>;
  }

  return (
    <main className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 md:px-6 lg:px-8">
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-primary mb-4">
            {cfcLocation.name}
          </h1>
        </header>

        <div className="space-y-12">
          <LocationGallery 
            images={cfcLocation.images} 
          />

          <LocationDetails 
            location={{
              name: cfcLocation.name,
              description: cfcLocation.description,
              features: cfcLocation.features.map((feature: string, index: number) => ({
                id: `feature-${index}`,
                name: feature
              }))
            }}
          />

          <LocationMap 
            coordinates={cfcLocation.coordinates}
            address={cfcLocation.address}
          />
        </div>
      </div>
    </main>
  );
};

export default CFCPage;
