import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { ChevronDownIcon, ChevronUpIcon, FunnelIcon } from '@heroicons/react/24/outline';
import type { RootState } from '../../../app/store';
import {
  setPriceRange,
  setLocation,
  setPropertyType,
  setListingType,
  resetFilters,
} from '../filtersSlice';

const PropertyFilters: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const filters = useSelector((state: RootState) => state.filters);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout>();

  const propertyTypes = [
    'apartment',
    'house',
    'villa',
    'office',
    'retail',
    'land'
  ];

  const handlePriceChange = (type: 'min' | 'max', value: string) => {
    const numValue = parseInt(value) || 0;
    dispatch(setPriceRange({
      ...filters.priceRange,
      [type]: numValue
    }));
  };

  const handleLocationChange = (value: string) => {
    clearTimeout(searchTimeout);
    setIsSearching(true);
    const timeout = setTimeout(() => {
      dispatch(setLocation(value));
      setIsSearching(false);
    }, 500);
    setSearchTimeout(timeout);
  };

  const handlePropertyTypeChange = (type: string) => {
    const newTypes = filters.propertyType.includes(type)
      ? filters.propertyType.filter(t => t !== type)
      : [...filters.propertyType, type];
    dispatch(setPropertyType(newTypes));
    setIsSearching(true);
    setTimeout(() => setIsSearching(false), 300);
  };

  const handleListingTypeChange = (type: 'all' | 'rent' | 'sale') => {
    dispatch(setListingType(type));
    setIsSearching(true);
    setTimeout(() => setIsSearching(false), 300);
  };

  const handleReset = () => {
    dispatch(resetFilters());
    setIsSearching(true);
    setTimeout(() => setIsSearching(false), 300);
  };

  return (
    <div className="relative mb-8">
      {/* Loading Indicator */}
      <AnimatePresence>
        {isSearching && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50 rounded-lg z-10 flex items-center justify-center"
          >
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              <span className="text-white font-medium">{t('properties.filters.searching')}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Filter Toggle Button */}
      <motion.button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between px-6 py-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <div className="flex items-center space-x-2">
          <FunnelIcon className="h-5 w-5 text-italian-green" />
          <span className="font-semibold">{t('properties.filters.title')}</span>
          {(filters.location || filters.propertyType.length > 0 || filters.listingType !== 'all' || 
            filters.priceRange.min !== 0 || filters.priceRange.max !== 3000000) && (
            <span className="ml-2 inline-flex items-center justify-center w-6 h-6 bg-italian-green text-white text-xs font-medium rounded-full">
              <span>!</span>
            </span>
          )}
        </div>
        {isExpanded ? (
          <ChevronUpIcon className="h-5 w-5 text-italian-green" />
        ) : (
          <ChevronDownIcon className="h-5 w-5 text-italian-green" />
        )}
      </motion.button>

      {/* Filters Panel */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
          >
            <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Price Range */}
              <div className="space-y-3">
                <h3 className="text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                  {t('properties.filters.priceRange.title')}
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="minPrice" className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                      {t('properties.filters.priceRange.min')}
                    </label>
                    <div className="relative">
                      <input
                        id="minPrice"
                        type="number"
                        value={filters.priceRange.min}
                        onChange={(e) => handlePriceChange('min', e.target.value)}
                        min="0"
                        step="1000"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-italian-green/50 focus:border-italian-green dark:bg-gray-700 dark:text-white text-sm"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 pointer-events-none">
                        <span className="text-xs">{t('properties.currency')}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label htmlFor="maxPrice" className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                      {t('properties.filters.priceRange.max')}
                    </label>
                    <div className="relative">
                      <input
                        id="maxPrice"
                        type="number"
                        value={filters.priceRange.max}
                        onChange={(e) => handlePriceChange('max', e.target.value)}
                        min="0"
                        step="1000"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-italian-green/50 focus:border-italian-green dark:bg-gray-700 dark:text-white text-sm"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 pointer-events-none">
                        <span className="text-xs">{t('properties.currency')}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mt-2">
                  <div 
                    className="h-full bg-italian-green"
                    style={{ 
                      width: `${Math.min(100, (filters.priceRange.max / 3000000) * 100)}%`,
                      marginLeft: `${Math.min(100, (filters.priceRange.min / 3000000) * 100)}%`
                    }}
                  />
                </div>
              </div>

              {/* Location */}
              <div className="space-y-3">
                <h3 className="text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                  {t('properties.filters.location.title')}
                </h3>
                <div className="relative">
                  <input
                    type="text"
                    value={filters.location}
                    onChange={(e) => handleLocationChange(e.target.value)}
                    placeholder={t('properties.filters.location.placeholder')}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-italian-green/50 focus:border-italian-green dark:bg-gray-700 dark:text-white text-sm pr-10"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Property Type */}
              <div className="space-y-3">
                <h3 className="text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                  {t('properties.filters.propertyType.title')}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {propertyTypes.map((type) => (
                    <button
                      key={type}
                      onClick={() => handlePropertyTypeChange(type)}
                      className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                        filters.propertyType.includes(type)
                          ? 'bg-italian-green text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {t(`properties.filters.propertyType.${type}`)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Listing Type */}
              <div className="space-y-3">
                <h3 className="text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                  {t('properties.filters.listingType.title')}
                </h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleListingTypeChange('all')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium flex-1 transition-colors ${
                      filters.listingType === 'all'
                        ? 'bg-italian-green text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {t('properties.filters.listingType.all')}
                  </button>
                  <button
                    onClick={() => handleListingTypeChange('rent')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium flex-1 transition-colors ${
                      filters.listingType === 'rent'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {t('properties.filters.listingType.rent')}
                  </button>
                  <button
                    onClick={() => handleListingTypeChange('sale')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium flex-1 transition-colors ${
                      filters.listingType === 'sale'
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {t('properties.filters.listingType.sale')}
                  </button>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="px-6 py-4 bg-gray-50 dark:bg-gray-900 border-t border-gray-100 dark:border-gray-700 flex justify-end">
              <button
                onClick={handleReset}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors mr-2"
              >
                {t('properties.filters.reset')}
              </button>
              <button
                onClick={() => setIsExpanded(false)}
                className="px-4 py-2 bg-italian-green text-white rounded-lg hover:bg-italian-green-dark transition-colors"
              >
                {t('properties.filters.apply')}
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PropertyFilters;
