-- Contact Messages Table
CREATE TABLE contact_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT NOT NULL CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$'),
    phone TEXT,
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    document_url TEXT,
    status TEXT NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'read', 'replied', 'archived')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_contact_messages_status ON contact_messages(status);
CREATE INDEX idx_contact_messages_created_at ON contact_messages(created_at DESC);

-- Career Applications Table
-- Stores job applications submitted by candidates through the careers section
CREATE TABLE career_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- Unique identifier for each application
    first_name TEXT NOT NULL, -- Applicant's first name
    last_name TEXT NOT NULL, -- Applicant's last name
    email TEXT NOT NULL CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$'), -- Applicant's email with format validation
    phone TEXT, -- Applicant's contact phone number (optional)
    career_id UUID NOT NULL REFERENCES careers(id), -- References the job position in the careers table
    career_title TEXT NOT NULL, -- Title of the job position (denormalized for query efficiency)
    resume_url TEXT NOT NULL, -- Storage URL for the uploaded resume document
    self_presentation TEXT, -- Applicant's self-introduction (optional)
    motivation TEXT, -- Applicant's motivation for applying (optional)
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'contacted', 'rejected')), -- Application status in the review process
    created_at TIMESTAMPTZ DEFAULT NOW(), -- Timestamp when application was submitted
    updated_at TIMESTAMPTZ DEFAULT NOW(), -- Timestamp when application was last updated
    user_id UUID -- Optional link to a registered user account if the applicant has one
);

-- Indexes for performance
CREATE INDEX idx_career_applications_status ON career_applications(status); -- For filtering applications by status
CREATE INDEX idx_career_applications_created_at ON career_applications(created_at DESC); -- For sorting by submission date (newest first)
CREATE INDEX idx_career_applications_career_id ON career_applications(career_id); -- For filtering applications by job position

-- Updated trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updating timestamps
CREATE TRIGGER update_contact_messages_updated_at
    BEFORE UPDATE ON contact_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_career_applications_updated_at
    BEFORE UPDATE ON career_applications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
