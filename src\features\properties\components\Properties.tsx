import React, { useMemo, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import type { RootState } from '../../../app/store';
import type { Property } from '../propertiesSlice';
import TypewriterText from '../../contact/components/TypewriterText';
import PropertyFilters from './PropertyFilters';

const Properties: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const properties = useSelector((state: RootState) => state.properties.items);
  const filters = useSelector((state: RootState) => state.filters);
  const [loadingPropertyId, setLoadingPropertyId] = useState<string | null>(null);

  const handlePropertyClick = (e: React.MouseEvent, propertyId: string) => {
    e.preventDefault();
    setLoadingPropertyId(propertyId);
    setTimeout(() => {
      navigate(`/properties/${propertyId}`);
    }, 300);
  };

  const filteredProperties = useMemo(() => {
    return properties.filter((property) => {
      // Filter by price range
      const price = property.price;
      if (price < filters.priceRange.min || price > filters.priceRange.max) {
        return false;
      }

      // Filter by location
      if (filters.location && !(property.location.city + ', ' + property.location.area).toLowerCase().includes(filters.location.toLowerCase())) {
        return false;
      }

      // Filter by property type
      if (filters.propertyType.length > 0 && !filters.propertyType.includes(property.type)) {
        return false;
      }

      // Filter by listing type
      if (filters.listingType !== 'all' && property.listingType !== filters.listingType) {
        return false;
      }

      return true;
    });
  }, [properties, filters]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen py-16 relative overflow-hidden">
      {/* Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-italian-green/10 to-transparent"></div>
      <div className="absolute top-40 left-20 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-italian-green/20 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <motion.div
          className="text-center mb-16 relative"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Badge */}
          <motion.div
            className="inline-block px-4 py-1 bg-italian-green/10 text-italian-green dark:text-italian-green-light dark:bg-italian-green/20 rounded-full mb-4 font-medium text-sm"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            {t('properties.exclusive')}
          </motion.div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 relative z-10">
            <TypewriterText text={t('properties.title')} delay={50} />
          </h1>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
            {t('properties.subtitle')}
          </p>
          
          {/* Animated Underline */}
          <motion.div
            className="h-1 w-24 bg-italian-green mx-auto rounded-full"
            initial={{ width: 0 }}
            animate={{ width: 96 }}
            transition={{ duration: 1, delay: 0.6 }}
          />
        </motion.div>

        {/* Filters */}
        <div className="mb-12">
          <PropertyFilters />
        </div>

        {/* Properties Grid */}
        <AnimatePresence>
          {filteredProperties.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-16 bg-white dark:bg-gray-800 rounded-xl shadow-lg"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">
                {t('properties.noResults')}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                {t('properties.tryAdjusting')}
              </p>
              <button
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="px-4 py-2 bg-italian-green text-white rounded-lg hover:bg-italian-green-dark transition-colors"
              >
                {t('properties.adjustFilters')}
              </button>
            </motion.div>
          ) : (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {filteredProperties.map((property) => {
                const { id, images, price, size, listingType, type } = property;
                const translations = property.translations[i18n.language as keyof typeof property.translations] || property.translations.en;
                const { title, description } = translations;

                return (
                  <motion.div
                    key={id}
                    className={`bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden relative ${
                      loadingPropertyId === id ? 'opacity-75 pointer-events-none' : ''
                    }`}
                    variants={itemVariants}
                    whileHover={{ y: -8, transition: { duration: 0.3 } }}
                  >
                    <Link
                      to={`/properties/${id}`}
                      onClick={(e) => handlePropertyClick(e, id)}
                      className="block h-full"
                    >
                      {/* Property Image */}
                      <div className="relative h-48 overflow-hidden group">
                        <motion.img
                          src={images[0]}
                          alt={title}
                          className="w-full h-full object-cover transition-transform group-hover:scale-110 duration-700"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                        
                        {/* Status Badge */}
                        <div className="absolute top-4 right-4">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                            listingType === 'rent'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                              : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          }`}>
                            {listingType === 'rent' ? t('properties.forRent') : t('properties.forSale')}
                          </span>
                        </div>
                        
                        <div className="absolute bottom-0 left-0 p-4">
                          <div className="flex space-x-2">
                            <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded text-white text-xs font-medium">
                              {type}
                            </span>
                            <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded text-white text-xs font-medium">
                              {size} m²
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      {/* Property Info */}
                      <div className="p-6">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 line-clamp-1">
                          {title}
                        </h3>
                        <p className="text-gray-500 dark:text-gray-400 mb-4 text-sm line-clamp-2">
                          {description}
                        </p>
                        
                        {/* Property details - beds and baths */}
                        <div className="flex items-center gap-3 mb-3">
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-1 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            <span className="text-sm text-gray-700 dark:text-gray-300">{type === 'studio' ? 1 : 2} {t('properties.beds', 'Beds')}</span>
                          </div>
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-1 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span className="text-sm text-gray-700 dark:text-gray-300">{type === 'studio' ? 1 : 2} {t('properties.baths', 'Baths')}</span>
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center mt-4">
                          <span className="text-lg md:text-xl font-semibold text-italian-green dark:text-italian-green-light">
                            {t('common.currency', { amount: price.toLocaleString() })}
                          </span>
                          <span className="inline-flex items-center text-gray-900 dark:text-white hover:text-italian-green dark:hover:text-italian-green-light transition-colors text-sm font-medium">
                            {t('properties.viewDetails')}
                            <svg className="w-4 h-4 ml-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                          </span>
                        </div>
                      </div>
                    </Link>
                    
                    {/* Loading Indicator */}
                    {loadingPropertyId === id && (
                      <div className="absolute inset-0 flex items-center justify-center bg-white/75 dark:bg-gray-800/75">
                        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-italian-green"></div>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Properties;