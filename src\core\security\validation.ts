import { z } from 'zod';

export const propertySchema = z.object({
  title: z.string().min(3).max(100),
  description: z.string().min(10).max(1000),
  price: z.number().positive(),
  location: z.object({
    city: z.string().min(2),
    area: z.string().min(2),
  }),
  size: z.number().positive(),
  type: z.enum(['studio', 'apartment', 'office']),
});

export const contactSchema = z.object({
  name: z.string().min(2).max(50),
  email: z.string().email(),
  phone: z.string().optional(),
  message: z.string().min(10).max(500),
});

export const validateProperty = (data: unknown) => {
  return propertySchema.safeParse(data);
};

export const validateContact = (data: unknown) => {
  return contactSchema.safeParse(data);
};
