import React from 'react';
import styled from 'styled-components';

interface LocationMapProps {
  coordinates: {
    lat: number;
    lng: number;
  };
  address: string;
}

const MapContainer = styled.div`
  width: 100%;
  min-height: 300px;
  border-radius: 8px;
  overflow: hidden;
  background-color: ${({ theme }) => theme.colors.background.secondary};
  margin: 1rem 0;
`;

const MapContent = styled.div`
  padding: 1.5rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const AddressText = styled.p`
  margin: 0.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const CoordinatesText = styled.p`
  margin: 0.5rem 0;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const LocationMap: React.FC<LocationMapProps> = ({ coordinates, address }) => {
  return (
    <MapContainer>
      <MapContent>
        <AddressText>{address}</AddressText>
        <CoordinatesText>
          Latitude: {coordinates.lat.toFixed(6)}°, Longitude: {coordinates.lng.toFixed(6)}°
        </CoordinatesText>
      </MapContent>
    </MapContainer>
  );
};

export default LocationMap;