{/* Previous imports remain the same */}
import React, { useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import PropertyMap from '../../shared/components/PropertyMap';
import VirtualTour from '../../shared/components/VirtualTour';
import type { RootState } from '../../../app/store';
import type { Property } from '../propertiesSlice';
import type { Agent } from '../agentSlice';

const PropertyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { t, i18n } = useTranslation();
  const [currentImage, setCurrentImage] = useState<number>(0);
  const property = useSelector((state: RootState) => 
    state.properties.items.find((p: Property) => p.id === id)
  );
  const agent = useSelector((state: RootState) => state.agent?.currentAgent);

  if (!property) {
    return (
      <div className="bg-gradient-to-br from-gray-50 via-gray-50/95 to-[#008C45]/5 dark:from-gray-900 dark:via-gray-900/95 dark:to-[#008C45]/10 min-h-screen py-16 relative overflow-hidden backdrop-blur-sm">
        {/* Decorative Elements */}
        <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-[#008C45]/10 to-transparent"></div>
        <div className="absolute top-40 left-20 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-[#008C45]/20 rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center py-16 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h1 className="text-3xl font-bold text-gray-700 dark:text-gray-200 mb-4">
              {t('properties.notFound')}
            </h1>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {t('properties.notFoundDescription')}
            </p>
            <Link
              to="/properties"
              className="px-6 py-3 bg-[#008C45] text-white rounded-lg hover:bg-[#006633] transition-colors inline-block"
            >
              {t('properties.backToProperties')}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const { translations, type, location, size, price, images, listingType } = property;
  const currentTranslation = translations[i18n.language as keyof typeof translations] || translations.en;

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <div className="bg-gradient-to-br from-gray-50 via-gray-50/95 to-[#008C45]/5 dark:from-gray-900 dark:via-gray-900/95 dark:to-[#008C45]/10 min-h-screen py-16 relative overflow-hidden backdrop-blur-sm">
      {/* Enhanced Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-96 bg-gradient-to-b from-[#008C45]/15 via-[#008C45]/5 to-transparent transform -skew-y-6 z-0"></div>
      <div className="absolute top-40 left-20 w-96 h-96 bg-blue-400/15 rounded-full blur-[128px] animate-pulse z-0"></div>
      <div className="absolute bottom-20 right-10 w-[32rem] h-[32rem] bg-[#008C45]/15 rounded-full blur-[128px] animate-pulse-slow z-0"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.015] dark:opacity-[0.03] pointer-events-none z-0"></div>
      
      <div className="container mx-auto px-4 relative z-20">
        <div className="max-w-5xl mx-auto">
          {/* Back to properties */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="mb-8"
          >
            <Link 
              to="/properties" 
              className="inline-flex items-center text-[#008C45] hover:text-[#006633] transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              {t('properties.backToProperties')}
            </Link>
          </motion.div>
          
          {/* Image Gallery */}
          <motion.div
            variants={fadeIn}
            initial="hidden"
            animate="visible"
            className="mb-12"
          >
            <div className="relative rounded-2xl overflow-hidden shadow-2xl ring-1 ring-black/5 dark:ring-white/5 transition-transform hover:scale-[1.01] duration-500">
              {/* Carousel */}
              <div className="relative z-10">
                <div className="flex transition-transform duration-500 ease-out h-[500px] overflow-hidden" style={{ transform: `translateX(-${currentImage * 100}%)` }}>
                  {images.map((image, index) => (
                    <div key={index} className="w-full h-full flex-shrink-0">
                      <img
                        src={image}
                        alt={`${currentTranslation.title} - Image ${index + 1}`}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                    </div>
                  ))}
                </div>
                {/* Enhanced Carousel Controls */}
                {images.length > 1 && (
                  <>
                    <button
                      className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white/90 hover:bg-white transition-colors flex items-center justify-center text-gray-800 hover:text-gray-900 shadow-lg hover:shadow-xl transform hover:scale-105 transition-transform z-30"
                      onClick={() => setCurrentImage((prev) => (prev === 0 ? images.length - 1 : prev - 1))}
                      aria-label="Previous image"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <button
                      className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white/90 hover:bg-white transition-colors flex items-center justify-center text-gray-800 hover:text-gray-900 shadow-lg hover:shadow-xl transform hover:scale-105 transition-transform z-30"
                      onClick={() => setCurrentImage((prev) => (prev === images.length - 1 ? 0 : prev + 1))}
                      aria-label="Next image"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                    
                    {/* Enhanced Carousel Indicators */}
                    <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex space-x-3 z-30">
                      {images.map((_, index) => (
                        <button
                          key={index}
                          className={`w-3 h-3 rounded-full transition-all duration-300 transform ${index === currentImage ? 'bg-white scale-125' : 'bg-white/50 hover:bg-white/75'}`}
                          onClick={() => setCurrentImage(index)}
                          aria-label={`Go to image ${index + 1}`}
                        />
                      ))}
                    </div>
                  </>
                )}
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent pointer-events-none z-20"></div>
              
              {/* Status Badge */}
              <div className="absolute top-6 right-6 z-30">
                <span className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold ${
                  listingType === 'rent'
                    ? 'bg-blue-500 text-white'
                    : 'bg-green-500 text-white'
                }`}>
                  <span className="w-2 h-2 rounded-full bg-white mr-2"></span>
                  {listingType === 'rent' ? t('properties.forRent') : t('properties.forSale')}
                </span>
              </div>
              
              {/* Property Info */}
              <div className="absolute bottom-0 left-0 w-full p-8 z-30">
                <div className="flex justify-between items-end">
                  <div>
                    <h1 className="text-3xl md:text-5xl font-bold text-white mb-2 text-shadow">
                      {currentTranslation.title}
                    </h1>
                    <p className="text-xl text-white/90 text-shadow flex items-center mb-4">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      {location.area}, {location.city}
                    </p>
                    <div className="flex space-x-4">
                      <span className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded text-white text-sm">
                        <svg className="w-4 h-4 mr-1 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        {type}
                      </span>
                      <span className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded text-white text-sm">
                        <svg className="w-4 h-4 mr-1 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path d="M5 5h14M5 5v14M5 5L19 19M19 5v14M19 5L5 19M5 19h14" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
                        </svg>
                        {size} m²
                      </span>
                    </div>
                  </div>
                  <div className="bg-[#008C45] text-white px-6 py-3 rounded-xl font-bold shadow-lg z-30">
                    <div className="flex items-center">
                      <span className="text-xl sm:text-2xl md:text-3xl font-bold text-white">
                        {t('common.currency', { amount: price.toLocaleString() })}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Rest of the component remains the same... */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            {/* Main Content */}
            <motion.div 
              className="lg:col-span-2"
              variants={fadeIn}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.2 }}
            >
              <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-xl shadow-lg p-8 mb-8 ring-1 ring-black/5 dark:ring-white/5 transition-all duration-300 hover:shadow-xl hover:scale-[1.01] z-10">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  {t('properties.aboutProperty')}
                </h2>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="text-gray-700 dark:text-gray-300">
                    {currentTranslation.description}
                  </p>
                </div>
              </div>

              {/* Features */}
              <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-xl shadow-lg p-8 mb-8 ring-1 ring-black/5 dark:ring-white/5 transition-all duration-300 hover:shadow-xl hover:scale-[1.01] z-10">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  {t('properties.features')}
                </h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                  {currentTranslation.features.map((feature, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 p-1">
                        <div className="w-5 h-5 rounded-full bg-[#008C45]/20 flex items-center justify-center">
                          <div className="w-2 h-2 rounded-full bg-[#008C45]"></div>
                        </div>
                      </div>
                      <span className="ml-2 text-gray-700 dark:text-gray-300">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Map */}
              <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-xl shadow-lg p-8 mb-8 ring-1 ring-black/5 dark:ring-white/5 transition-all duration-300 hover:shadow-xl hover:scale-[1.01] z-10">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  {t('properties.location')}
                </h2>
                <div className="h-80 rounded-lg overflow-hidden">
                  <PropertyMap location={location} />
                </div>
              </div>

              {/* Virtual Tour */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 z-10">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  {t('properties.virtualTour')}
                </h2>
                <div className="h-80 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                  <VirtualTour propertyId={id} />
                </div>
              </div>
            </motion.div>

            {/* Sidebar */}
            <motion.div
              className="lg:col-span-1"
              variants={fadeIn}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.4 }}
            >
              {/* Contact Card */}
              <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-xl shadow-lg p-8 mb-8 sticky top-8 ring-1 ring-black/5 dark:ring-white/5 transition-all duration-300 hover:shadow-xl z-10">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  {t('properties.interested')}
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {t('properties.interestedText')}
                </p>
                
                <div className="space-y-4">
                  <button className="w-full bg-gradient-to-r from-[#008C45] to-[#006633] text-white py-3 px-4 rounded-lg hover:from-[#006633] hover:to-[#008C45] transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg flex items-center justify-center group">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {t('properties.schedule')}
                  </button>
                  
                  <button className="w-full border-2 border-[#008C45] text-[#008C45] py-3 px-4 rounded-lg hover:bg-[#008C45]/5 transition-all duration-300 transform hover:scale-[1.02] flex items-center justify-center group">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    {t('properties.contact')}
                  </button>
                </div>
                
                {/* Agent Information */}
                {agent && (
                  <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600 mr-4">
                        <img 
                          src={agent.image} 
                          alt={agent.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white">{agent.name}</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{agent.title}</p>
                      </div>
                    </div>
                    <div className="mt-4 space-y-2">
                      <a href={`tel:${agent.phone}`} className="flex items-center text-gray-600 dark:text-gray-400 hover:text-[#008C45] dark:hover:text-[#00B358]">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        {agent.phone}
                      </a>
                      <a href={`mailto:${agent.email}`} className="flex items-center text-gray-600 dark:text-gray-400 hover:text-[#008C45] dark:hover:text-[#00B358]">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        {agent.email}
                      </a>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Similar Properties Teaser */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 z-10">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  {t('properties.similarProperties')}
                </h2>
                <Link 
                  to="/properties" 
                  className="block group overflow-hidden bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-xl transition-shadow relative"
                >
                  <div className="relative h-60 overflow-hidden">
                    <img 
                      src="https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                      alt="Similar property"
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                    />
                    <div className="absolute top-4 right-4">
                      <span className="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        {t('properties.forSale')}
                      </span>
                    </div>
                    <div className="absolute bottom-0 left-0 p-4">
                      <div className="flex space-x-2">
                        <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded text-white text-xs font-medium">
                          {t('properties.types.apartment')}
                        </span>
                        <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded text-white text-xs font-medium">
                          120 m²
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 line-clamp-1">
                      {t('properties.viewSimilar')}
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-4 text-sm line-clamp-2">
                      {t('properties.findMore')}
                    </p>
                    <div className="flex justify-between items-center mt-4">
                      <span className="text-lg md:text-xl font-semibold text-[#008C45] dark:text-[#00B358]">
                        {t('common.currency', { amount: '1,200,000' })}
                      </span>
                      <span className="inline-flex items-center text-gray-900 dark:text-white hover:text-[#008C45] dark:hover:text-[#00B358] transition-colors text-sm font-medium">
                        {t('properties.viewAll')}
                        <svg className="w-4 h-4 ml-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </span>
                    </div>
                  </div>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertyDetail;
