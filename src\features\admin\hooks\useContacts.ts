import { useCallback } from 'react';
import { toast } from 'react-toastify';
import { useSupabaseQuery } from '@core/hooks/useSupabaseQuery';
import { useTranslation } from 'react-i18next';

// Constants for retry mechanism and logging
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 1000;
const LOG_PREFIX = '[useContacts]';

export type MessageStatus = 'new' | 'read' | 'replied' | 'archived';

export interface Contact {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  document_url?: string;
  created_at: string;
  status: MessageStatus;  
}

const VALID_STATUS_TRANSITIONS: Record<MessageStatus, MessageStatus[]> = {
  new: ['read', 'archived'],
  read: ['replied', 'archived'],
  replied: ['archived'],
  archived: ['new']
};

export const useContacts = () => {
  const { t } = useTranslation();
  const [supabaseConnected, setSupabaseConnected] = useState(false);
  
  // Use the enhanced useSupabaseQuery hook with retry capability
  const query = useSupabaseQuery<Contact>({
    table: 'contact_messages',
    orderBy: { column: 'created_at', ascending: false },
    retryLimit: 3
  });

  // Check Supabase connection on mount
  useEffect(() => {
    checkSupabaseConnection();
  }, []);

  // Function to check Supabase connection
  const checkSupabaseConnection = async () => {
    try {
      console.log(`${LOG_PREFIX} Verifying Supabase connection...`);
      const { data, error } = await supabase.from('contact_messages').select('count');
      
      if (error) {
        console.error(`${LOG_PREFIX} Supabase connection check failed:`, error);
        setSupabaseConnected(false);
        return false;
      }
      
      console.log(`${LOG_PREFIX} Supabase connection verified successfully`);
      setSupabaseConnected(true);
      return true;
    } catch (err) {
      console.error(`${LOG_PREFIX} Failed to verify Supabase connection:`, err);
      setSupabaseConnected(false);
      return false;
    }
  };

  const updateContactStatus = useCallback(async (id: string, newStatus: MessageStatus) => {
    try {
      console.log(`${LOG_PREFIX} Updating contact ${id} status to ${newStatus}`);
      const contact = query.data.find(c => c.id === id);
      
      if (!contact) {
        console.error(`${LOG_PREFIX} Contact not found: ${id}`);
        throw new Error(t('admin.contacts.errors.notFound', 'Contact not found'));
      }

      const validTransitions = VALID_STATUS_TRANSITIONS[contact.status];
      if (!validTransitions.includes(newStatus)) {
        console.error(`${LOG_PREFIX} Invalid status transition from ${contact.status} to ${newStatus}`);
        throw new Error(t('admin.contacts.errors.invalidTransition', 'Invalid status transition'));
      }

      const result = await query.updateRecord(id, { status: newStatus });
      console.log(`${LOG_PREFIX} Status updated successfully for contact ${id}`);
      toast.success(t('admin.contacts.status.updated', 'Status updated successfully'));
      return result;
    } catch (err) {
      console.error(`${LOG_PREFIX} Error updating contact status:`, err);
      toast.error(err instanceof Error ? err.message : t('admin.contacts.status.error', 'Failed to update status'));
      return false;
    }
  }, [query.data, query.updateRecord, t]);

  // Get connection info for debugging
  const getConnectionInfo = useCallback(() => {
    return {
      connected: supabaseConnected,
      lastChecked: new Date().toISOString(),
      lastFetched: query.lastFetched ? new Date(query.lastFetched).toISOString() : null,
      dataAge: query.lastFetched ? Date.now() - query.lastFetched : null,
      retryCount: query.retryCount
    };
  }, [supabaseConnected, query.lastFetched, query.retryCount]);

  return {
    contacts: query.data,
    loading: query.loading,
    error: query.error,
    errorDetails: query.errorDetails,
    lastFetched: query.lastFetched,
    connectionInfo: getConnectionInfo(),
    refetch: query.refetch,
    updateContactStatus,
    checkConnection: checkSupabaseConnection
  };
};